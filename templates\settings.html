{% extends "base.html" %}

{% block title %}Configuración - Sistema de Análisis de Loterías{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1>
            <i class="fas fa-cog"></i> Configuración del Sistema
        </h1>
        <p class="text-muted">
            Personaliza los parámetros del sistema según tus preferencias.
        </p>
    </div>
</div>

<!-- Analysis Settings -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line"></i> Configuración de Análisis
                </h5>
            </div>
            <div class="card-body">
                <form id="analysisSettingsForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="euromillonesYears" class="form-label">Años de análisis - Euromillones</label>
                                <select class="form-select" id="euromillonesYears" name="euromillones_analysis_years">
                                    <option value="1">1 año</option>
                                    <option value="2">2 años</option>
                                    <option value="5" selected>5 años</option>
                                    <option value="10">10 años</option>
                                    <option value="15">15 años</option>
                                    <option value="20">20 años</option>
                                </select>
                                <div class="form-text">Período de datos históricos para análisis de Euromillones</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="lotoFranceYears" class="form-label">Años de análisis - Loto Francia</label>
                                <select class="form-select" id="lotoFranceYears" name="loto_france_analysis_years">
                                    <option value="1">1 año</option>
                                    <option value="2">2 años</option>
                                    <option value="5" selected>5 años</option>
                                    <option value="10">10 años</option>
                                    <option value="15">15 años</option>
                                    <option value="20">20 años</option>
                                </select>
                                <div class="form-text">Período de datos históricos para análisis de Loto Francia</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="defaultCombinations" class="form-label">Combinaciones por defecto</label>
                                <select class="form-select" id="defaultCombinations" name="default_combinations_count">
                                    <option value="5">5 combinaciones</option>
                                    <option value="10" selected>10 combinaciones</option>
                                    <option value="15">15 combinaciones</option>
                                    <option value="20">20 combinaciones</option>
                                    <option value="25">25 combinaciones</option>
                                </select>
                                <div class="form-text">Número de combinaciones a generar por defecto</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="autoUpdate" class="form-label">Actualización automática</label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="autoUpdate" name="auto_update_enabled" checked>
                                    <label class="form-check-label" for="autoUpdate">
                                        Activar actualización automática de datos
                                    </label>
                                </div>
                                <div class="form-text">Actualizar datos automáticamente desde fuentes web</div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Machine Learning Settings -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-brain"></i> Configuración de Machine Learning
                </h5>
            </div>
            <div class="card-body">
                <form id="mlSettingsForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="neuralEpochs" class="form-label">Épocas de entrenamiento (Red Neuronal)</label>
                                <input type="range" class="form-range" id="neuralEpochs" name="neural_network_epochs" 
                                       min="50" max="500" step="50" value="100">
                                <div class="d-flex justify-content-between">
                                    <small>50</small>
                                    <small id="neuralEpochsValue">100</small>
                                    <small>500</small>
                                </div>
                                <div class="form-text">Más épocas = mejor precisión pero más tiempo de entrenamiento</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="markovOrder" class="form-label">Orden de Cadena de Markov</label>
                                <select class="form-select" id="markovOrder" name="markov_chain_order">
                                    <option value="2">2 (Rápido)</option>
                                    <option value="3" selected>3 (Balanceado)</option>
                                    <option value="4">4 (Preciso)</option>
                                    <option value="5">5 (Muy preciso)</option>
                                </select>
                                <div class="form-text">Mayor orden = más precisión pero requiere más datos</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="defaultModel" class="form-label">Modelo por defecto</label>
                                <select class="form-select" id="defaultModel" name="default_prediction_model">
                                    <option value="frequency">Solo Frecuencias</option>
                                    <option value="markov">Cadenas de Markov</option>
                                    <option value="neural">Red Neuronal</option>
                                    <option value="combined" selected>Modelo Combinado</option>
                                </select>
                                <div class="form-text">Modelo a usar por defecto para predicciones</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="trainingData" class="form-label">Datos para entrenamiento</label>
                                <select class="form-select" id="trainingData" name="training_data_years">
                                    <option value="2">2 años</option>
                                    <option value="3">3 años</option>
                                    <option value="5" selected>5 años</option>
                                    <option value="7">7 años</option>
                                    <option value="10">10 años</option>
                                </select>
                                <div class="form-text">Cantidad de datos históricos para entrenar modelos ML</div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Display Settings -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-palette"></i> Configuración de Visualización
                </h5>
            </div>
            <div class="card-body">
                <form id="displaySettingsForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="chartTheme" class="form-label">Tema de gráficos</label>
                                <select class="form-select" id="chartTheme" name="chart_theme">
                                    <option value="default" selected>Por defecto</option>
                                    <option value="dark">Oscuro</option>
                                    <option value="colorful">Colorido</option>
                                    <option value="minimal">Minimalista</option>
                                </select>
                                <div class="form-text">Estilo visual de los gráficos y visualizaciones</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="itemsPerPage" class="form-label">Elementos por página</label>
                                <select class="form-select" id="itemsPerPage" name="items_per_page">
                                    <option value="25">25</option>
                                    <option value="50" selected>50</option>
                                    <option value="100">100</option>
                                    <option value="200">200</option>
                                </select>
                                <div class="form-text">Número de elementos a mostrar en tablas paginadas</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Opciones de visualización</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="showTooltips" name="show_tooltips" checked>
                                    <label class="form-check-label" for="showTooltips">
                                        Mostrar tooltips informativos
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="animateCharts" name="animate_charts" checked>
                                    <label class="form-check-label" for="animateCharts">
                                        Animar gráficos
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="compactMode" name="compact_mode">
                                    <label class="form-check-label" for="compactMode">
                                        Modo compacto
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="language" class="form-label">Idioma</label>
                                <select class="form-select" id="language" name="language">
                                    <option value="es" selected>Español</option>
                                    <option value="en">English</option>
                                    <option value="fr">Français</option>
                                </select>
                                <div class="form-text">Idioma de la interfaz (requiere reinicio)</div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Data Management -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-database"></i> Gestión de Datos
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Información de la Base de Datos</h6>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Sorteos de Euromillones:</span>
                                <span class="badge bg-primary" id="euromillonesCount">-</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>Sorteos de Loto Francia:</span>
                                <span class="badge bg-info" id="lotoFranceCount">-</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>Predicciones generadas:</span>
                                <span class="badge bg-success" id="predictionsCount">-</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>Tamaño de base de datos:</span>
                                <span class="badge bg-secondary" id="dbSize">-</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>Acciones de Mantenimiento</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary" onclick="updateAllData()">
                                <i class="fas fa-sync-alt"></i> Actualizar Todos los Datos
                            </button>
                            <button class="btn btn-outline-warning" onclick="clearPredictions()">
                                <i class="fas fa-trash"></i> Limpiar Predicciones Antiguas
                            </button>
                            <button class="btn btn-outline-info" onclick="exportDatabase()">
                                <i class="fas fa-download"></i> Exportar Base de Datos
                            </button>
                            <button class="btn btn-outline-danger" onclick="resetSettings()">
                                <i class="fas fa-undo"></i> Restaurar Configuración
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Save Settings -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center">
                <button class="btn btn-success btn-lg me-3" onclick="saveAllSettings()">
                    <i class="fas fa-save"></i> Guardar Configuración
                </button>
                <button class="btn btn-outline-secondary btn-lg" onclick="loadCurrentSettings()">
                    <i class="fas fa-undo"></i> Recargar
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Update neural epochs display
    document.getElementById('neuralEpochs').addEventListener('input', function() {
        document.getElementById('neuralEpochsValue').textContent = this.value;
    });

    // Load current settings
    document.addEventListener('DOMContentLoaded', function() {
        loadCurrentSettings();
        loadDatabaseInfo();
    });

    function loadCurrentSettings() {
        // Load settings from server
        fetch('/api/settings')
            .then(response => response.json())
            .then(settings => {
                // Populate form fields with current settings
                for (const [key, value] of Object.entries(settings)) {
                    const element = document.querySelector(`[name="${key}"]`);
                    if (element) {
                        if (element.type === 'checkbox') {
                            element.checked = value;
                        } else {
                            element.value = value;
                        }
                    }
                }
                
                // Update neural epochs display
                const neuralEpochs = document.getElementById('neuralEpochs');
                if (neuralEpochs) {
                    document.getElementById('neuralEpochsValue').textContent = neuralEpochs.value;
                }
            })
            .catch(error => {
                console.error('Error loading settings:', error);
            });
    }

    function loadDatabaseInfo() {
        fetch('/api/database_info')
            .then(response => response.json())
            .then(info => {
                document.getElementById('euromillonesCount').textContent = info.euromillones_count || 0;
                document.getElementById('lotoFranceCount').textContent = info.loto_france_count || 0;
                document.getElementById('predictionsCount').textContent = info.predictions_count || 0;
                document.getElementById('dbSize').textContent = info.db_size || 'N/A';
            })
            .catch(error => {
                console.error('Error loading database info:', error);
            });
    }

    function saveAllSettings() {
        const forms = ['analysisSettingsForm', 'mlSettingsForm', 'displaySettingsForm'];
        const allSettings = {};

        forms.forEach(formId => {
            const form = document.getElementById(formId);
            const formData = new FormData(form);
            
            for (const [key, value] of formData.entries()) {
                const element = form.querySelector(`[name="${key}"]`);
                if (element.type === 'checkbox') {
                    allSettings[key] = element.checked;
                } else if (element.type === 'number' || element.type === 'range') {
                    allSettings[key] = parseInt(value);
                } else {
                    allSettings[key] = value;
                }
            }
        });

        fetch('/save_settings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(allSettings)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', 'Configuración guardada correctamente');
            } else {
                showAlert('danger', 'Error al guardar configuración: ' + data.message);
            }
        })
        .catch(error => {
            showAlert('danger', 'Error de conexión: ' + error.message);
        });
    }

    function updateAllData() {
        showAlert('info', 'Iniciando actualización de datos...');
        
        fetch('/update_data')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', data.message);
                    loadDatabaseInfo(); // Refresh database info
                } else {
                    showAlert('danger', 'Error: ' + data.error);
                }
            })
            .catch(error => {
                showAlert('danger', 'Error de conexión: ' + error.message);
            });
    }

    function clearPredictions() {
        if (confirm('¿Estás seguro de que quieres eliminar todas las predicciones antiguas?')) {
            fetch('/api/clear_predictions', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAlert('success', 'Predicciones eliminadas correctamente');
                        loadDatabaseInfo();
                    } else {
                        showAlert('danger', 'Error: ' + data.message);
                    }
                })
                .catch(error => {
                    showAlert('danger', 'Error de conexión: ' + error.message);
                });
        }
    }

    function exportDatabase() {
        showAlert('info', 'Iniciando exportación de base de datos...');
        window.open('/api/export_database', '_blank');
    }

    function resetSettings() {
        if (confirm('¿Estás seguro de que quieres restaurar la configuración por defecto?')) {
            fetch('/api/reset_settings', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAlert('success', 'Configuración restaurada');
                        loadCurrentSettings();
                    } else {
                        showAlert('danger', 'Error: ' + data.message);
                    }
                })
                .catch(error => {
                    showAlert('danger', 'Error de conexión: ' + error.message);
                });
        }
    }
</script>
{% endblock %}
