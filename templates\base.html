<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Sistema de Análisis de Loterías{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        .navbar-brand {
            font-weight: bold;
        }
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: 1px solid rgba(0, 0, 0, 0.125);
        }
        .number-ball {
            display: inline-block;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            line-height: 40px;
            margin: 2px;
            font-weight: bold;
        }
        .star-ball {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .chance-ball {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .hot-number {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        }
        .cold-number {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        }
        .frequency-chart {
            height: 400px;
        }
        .pattern-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .disclaimer {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 0.375rem;
            padding: 1rem;
            margin: 1rem 0;
        }
        .loading {
            display: none;
        }
        .loading.show {
            display: block;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-chart-line"></i> Sistema de Análisis de Loterías
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">
                            <i class="fas fa-home"></i> Inicio
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-chart-bar"></i> Análisis
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('lottery_analysis', lottery_type='euromillones') }}">Euromillones</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('lottery_analysis', lottery_type='loto_france') }}">Loto Francia</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-crystal-ball"></i> Predicciones
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('predictions', lottery_type='euromillones') }}">Euromillones</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('predictions', lottery_type='loto_france') }}">Loto Francia</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-history"></i> Historial
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('history', lottery_type='euromillones') }}">Euromillones</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('history', lottery_type='loto_france') }}">Loto Francia</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('import_data_page') }}">
                            <i class="fas fa-upload"></i> Importar Datos
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="updateData()">
                            <i class="fas fa-sync-alt"></i> Actualizar Datos
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('settings') }}">
                            <i class="fas fa-cog"></i> Configuración
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else 'warning' if category == 'warning' else 'success' }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Main Content -->
    <main class="container mt-4">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-light mt-5 py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>Sistema de Análisis de Loterías</h5>
                    <p class="text-muted">Análisis estadístico y predicciones para Euromillones y Loto Francia</p>
                </div>
                <div class="col-md-6">
                    <div class="disclaimer">
                        <h6><i class="fas fa-exclamation-triangle"></i> Aviso Importante</h6>
                        <p class="mb-0 small">
                            Las loterías son juegos de azar completamente aleatorios. 
                            Las predicciones y análisis estadísticos mostrados en este sistema 
                            son únicamente para fines educativos y de entretenimiento. 
                            No garantizan resultados y no deben utilizarse como base para decisiones financieras.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Common JavaScript -->
    <script>
        // Update data function
        function updateData() {
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Actualizando...';
            button.disabled = true;
            
            fetch('/update_data')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAlert('success', data.message);
                    } else {
                        showAlert('danger', data.error || 'Error al actualizar datos');
                    }
                })
                .catch(error => {
                    showAlert('danger', 'Error de conexión: ' + error.message);
                })
                .finally(() => {
                    button.innerHTML = originalText;
                    button.disabled = false;
                });
        }
        
        // Show alert function
        function showAlert(type, message) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            const container = document.querySelector('.container');
            container.insertBefore(alertDiv, container.firstChild);
            
            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }
        
        // Format number as lottery ball
        function formatNumberBall(number, type = 'main') {
            const ballClass = type === 'star' ? 'star-ball' : type === 'chance' ? 'chance-ball' : '';
            return `<span class="number-ball ${ballClass}">${number}</span>`;
        }
        
        // Format date
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('es-ES');
        }
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
