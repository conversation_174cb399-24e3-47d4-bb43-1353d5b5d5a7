"""
Main Flask application for Lottery Analysis System
"""
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
import os
import json
from datetime import datetime, timedelta
import logging
from werkzeug.utils import secure_filename

# Import our modules
from config import config
from models import db, LotteryDraw, NumberFrequency, PredictionResult, UserSettings
from statistical_analysis import LotteryStatistics, analyze_all_lotteries
from ml_models import CombinedPredictor
from data_scraper import update_all_lottery_data
from data_importer import DataImporter

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_app(config_name='default'):
    """Create Flask application"""
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # Initialize database
    db.init_app(app)
    
    # Create database tables
    with app.app_context():
        # Create database directory if it doesn't exist
        os.makedirs('database', exist_ok=True)
        os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
        os.makedirs('logs', exist_ok=True)

        # Only create tables if we're not importing this module
        if __name__ == '__main__':
            db.create_all()
    
    return app

app = create_app()

@app.route('/')
def index():
    """Main dashboard"""
    try:
        # Get basic statistics
        euromillones_count = LotteryDraw.query.filter_by(lottery_type='euromillones').count()
        loto_france_count = LotteryDraw.query.filter_by(lottery_type='loto_france').count()
        
        # Get latest draws
        latest_euromillones = LotteryDraw.query.filter_by(lottery_type='euromillones').order_by(LotteryDraw.draw_date.desc()).first()
        latest_loto_france = LotteryDraw.query.filter_by(lottery_type='loto_france').order_by(LotteryDraw.draw_date.desc()).first()
        
        return render_template('index.html',
                             euromillones_count=euromillones_count,
                             loto_france_count=loto_france_count,
                             latest_euromillones=latest_euromillones,
                             latest_loto_france=latest_loto_france)
    except Exception as e:
        logger.error(f"Error in index route: {e}")
        return render_template('error.html', error=str(e))

@app.route('/lottery/<lottery_type>')
def lottery_analysis(lottery_type):
    """Lottery analysis page"""
    if lottery_type not in ['euromillones', 'loto_france']:
        return redirect(url_for('index'))

    try:
        stats = LotteryStatistics(lottery_type)

        # Get years parameter from URL or user settings
        years = request.args.get('years', type=int)
        if not years:
            years_setting = UserSettings.query.filter_by(setting_key=f'{lottery_type}_analysis_years').first()
            years = years_setting.get_value() if years_setting else app.config['DEFAULT_ANALYSIS_YEARS']

        # Calculate statistics
        frequencies = stats.calculate_number_frequencies(years)
        probabilities = stats.calculate_combination_probabilities()
        patterns = stats.analyze_patterns(years)

        return render_template('lottery_analysis.html',
                             lottery_type=lottery_type,
                             frequencies=frequencies,
                             probabilities=probabilities,
                             patterns=patterns,
                             years=years)
    except Exception as e:
        logger.error(f"Error in lottery analysis: {e}")
        return render_template('error.html', error=str(e))

@app.route('/predictions/<lottery_type>')
def predictions(lottery_type):
    """Predictions page"""
    if lottery_type not in ['euromillones', 'loto_france']:
        return redirect(url_for('index'))
    
    try:
        # Get recent predictions
        recent_predictions = PredictionResult.query.filter_by(
            lottery_type=lottery_type
        ).order_by(PredictionResult.created_at.desc()).limit(20).all()
        
        return render_template('predictions.html',
                             lottery_type=lottery_type,
                             predictions=recent_predictions)
    except Exception as e:
        logger.error(f"Error in predictions route: {e}")
        return render_template('error.html', error=str(e))

@app.route('/generate_predictions/<lottery_type>', methods=['POST'])
def generate_predictions(lottery_type):
    """Generate new predictions"""
    if lottery_type not in ['euromillones', 'loto_france']:
        return jsonify({'error': 'Invalid lottery type'}), 400
    
    try:
        # Get parameters from request
        num_combinations = request.json.get('num_combinations', 10)
        model_type = request.json.get('model_type', 'combined')
        
        # Create predictor and generate predictions
        predictor = CombinedPredictor(lottery_type)
        
        if model_type == 'combined':
            # Train models if needed
            training_results = predictor.train_all_models()
            predictions = predictor.generate_predictions(num_combinations)
        else:
            # Use specific model (simplified for now)
            predictions = predictor.generate_predictions(num_combinations)
        
        # Save predictions to database
        saved_count = predictor.save_predictions_to_db(predictions)
        
        return jsonify({
            'success': True,
            'message': f'Generated {len(predictions)} predictions',
            'saved_count': saved_count,
            'predictions': predictions
        })
        
    except Exception as e:
        logger.error(f"Error generating predictions: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/history/<lottery_type>')
def history(lottery_type):
    """Historical data page"""
    if lottery_type not in ['euromillones', 'loto_france']:
        return redirect(url_for('index'))

    try:
        page = request.args.get('page', 1, type=int)
        per_page = 50

        # Get filter parameters
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        search_number = request.args.get('search_number', type=int)
        export_format = request.args.get('export')

        # Build query
        query = LotteryDraw.query.filter_by(lottery_type=lottery_type)

        # Apply filters
        if date_from:
            try:
                from_date = datetime.strptime(date_from, '%Y-%m-%d').date()
                query = query.filter(LotteryDraw.draw_date >= from_date)
            except ValueError:
                pass

        if date_to:
            try:
                to_date = datetime.strptime(date_to, '%Y-%m-%d').date()
                query = query.filter(LotteryDraw.draw_date <= to_date)
            except ValueError:
                pass

        if search_number:
            # Search in both main and additional numbers
            query = query.filter(
                db.or_(
                    LotteryDraw.main_numbers.contains(f'[{search_number},') |
                    LotteryDraw.main_numbers.contains(f', {search_number},') |
                    LotteryDraw.main_numbers.contains(f', {search_number}]') |
                    LotteryDraw.additional_numbers.contains(f'[{search_number},') |
                    LotteryDraw.additional_numbers.contains(f', {search_number},') |
                    LotteryDraw.additional_numbers.contains(f', {search_number}]')
                )
            )

        # Handle export
        if export_format == 'csv':
            return export_history_csv(query.all(), lottery_type)

        # Get paginated results
        draws = query.order_by(LotteryDraw.draw_date.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        return render_template('history.html',
                             lottery_type=lottery_type,
                             draws=draws)
    except Exception as e:
        logger.error(f"Error in history route: {e}")
        return render_template('error.html', error=str(e))

def export_history_csv(draws, lottery_type):
    """Export historical data as CSV"""
    try:
        import csv
        from io import StringIO
        from flask import Response

        output = StringIO()
        writer = csv.writer(output)

        # Write header
        if lottery_type == 'euromillones':
            header = ['Fecha', 'Num1', 'Num2', 'Num3', 'Num4', 'Num5', 'Estrella1', 'Estrella2', 'Bote', 'Ganadores']
        else:
            header = ['Fecha', 'Num1', 'Num2', 'Num3', 'Num4', 'Num5', 'Chance', 'Bote', 'Ganadores']

        writer.writerow(header)

        # Write data
        for draw in draws:
            main_numbers = draw.get_main_numbers()
            additional_numbers = draw.get_additional_numbers()

            if lottery_type == 'euromillones':
                row = [
                    draw.draw_date.strftime('%Y-%m-%d'),
                    *main_numbers,
                    *additional_numbers,
                    draw.jackpot_amount or '',
                    draw.winners_count or ''
                ]
            else:
                row = [
                    draw.draw_date.strftime('%Y-%m-%d'),
                    *main_numbers,
                    additional_numbers[0] if additional_numbers else '',
                    draw.jackpot_amount or '',
                    draw.winners_count or ''
                ]

            writer.writerow(row)

        output.seek(0)
        filename = f"historial_{lottery_type}_{datetime.now().strftime('%Y%m%d')}.csv"

        return Response(
            output.getvalue(),
            mimetype='text/csv',
            headers={'Content-Disposition': f'attachment; filename={filename}'}
        )
    except Exception as e:
        logger.error(f"Error exporting CSV: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/education')
def education():
    """Educational page about statistics and probability"""
    return render_template('education.html')

@app.route('/import_data')
def import_data_page():
    """Data import page"""
    return render_template('import_data.html')

@app.route('/upload_data', methods=['POST'])
def upload_data():
    """Handle file upload for data import"""
    try:
        if 'file' not in request.files:
            flash('No file selected', 'error')
            return redirect(url_for('import_data_page'))
        
        file = request.files['file']
        lottery_type = request.form.get('lottery_type')
        
        if file.filename == '':
            flash('No file selected', 'error')
            return redirect(url_for('import_data_page'))
        
        if lottery_type not in ['euromillones', 'loto_france']:
            flash('Invalid lottery type', 'error')
            return redirect(url_for('import_data_page'))
        
        # Save uploaded file
        filename = secure_filename(file.filename)
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(file_path)
        
        # Import data
        from data_importer import DataImporter
        importer = DataImporter()
        result = importer.import_to_database(file_path, lottery_type)

        # Clean up uploaded file
        os.remove(file_path)

        # Log detailed results for debugging
        logger.info(f"Import result: {result}")

        if result['success']:
            message = f"✅ Successfully imported {result['saved_count']} new draws"

            # Add update info if any
            if result.get('updated_count', 0) > 0:
                message += f", updated {result['updated_count']} existing draws"

            # Add duplicate info if any
            if result.get('duplicate_count', 0) > 0:
                message += f", skipped {result['duplicate_count']} duplicates"

            flash(message, 'success')
        else:
            flash(f"❌ Import failed: {result['message']}", 'error')
            logger.error(f"Import failed for {lottery_type}: {result}")

        if result.get('errors'):
            error_details = "; ".join(result['errors'][:5])  # Show first 5 errors
            if len(result['errors']) > 5:
                error_details += f"... and {len(result['errors']) - 5} more"
            flash(f"⚠️ Validation errors ({len(result['errors'])} total): {error_details}", 'warning')
        
        return redirect(url_for('import_data_page'))
        
    except Exception as e:
        logger.error(f"Error uploading data: {e}")
        flash(f"Upload failed: {str(e)}", 'error')
        return redirect(url_for('import_data_page'))

@app.route('/update_data')
def update_data():
    """Update lottery data from web sources"""
    try:
        saved_count = update_all_lottery_data()
        return jsonify({
            'success': True,
            'message': f'Updated data successfully. {saved_count} new draws added.',
            'saved_count': saved_count
        })
    except Exception as e:
        logger.error(f"Error updating data: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/load_historical_data')
def load_historical_data():
    """Load historical lottery data"""
    try:
        years = request.args.get('years', 2, type=int)

        from real_data_loader import load_all_historical_data
        total_saved = load_all_historical_data(years)

        return jsonify({
            'success': True,
            'message': f'Loaded {total_saved} historical draws for {years} years.',
            'total_saved': total_saved,
            'years_loaded': years
        })
    except Exception as e:
        logger.error(f"Error loading historical data: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/data_status')
def data_status():
    """Get current data status"""
    try:
        # Count draws by lottery type
        euromillones_count = LotteryDraw.query.filter_by(lottery_type='euromillones').count()
        loto_france_count = LotteryDraw.query.filter_by(lottery_type='loto_france').count()

        # Get date ranges
        euromillones_dates = db.session.query(
            db.func.min(LotteryDraw.draw_date),
            db.func.max(LotteryDraw.draw_date)
        ).filter_by(lottery_type='euromillones').first()

        loto_france_dates = db.session.query(
            db.func.min(LotteryDraw.draw_date),
            db.func.max(LotteryDraw.draw_date)
        ).filter_by(lottery_type='loto_france').first()

        return jsonify({
            'euromillones': {
                'count': euromillones_count,
                'date_range': {
                    'from': euromillones_dates[0].isoformat() if euromillones_dates[0] else None,
                    'to': euromillones_dates[1].isoformat() if euromillones_dates[1] else None
                }
            },
            'loto_france': {
                'count': loto_france_count,
                'date_range': {
                    'from': loto_france_dates[0].isoformat() if loto_france_dates[0] else None,
                    'to': loto_france_dates[1].isoformat() if loto_france_dates[1] else None
                }
            },
            'total_draws': euromillones_count + loto_france_count
        })
    except Exception as e:
        logger.error(f"Error getting data status: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/frequencies/<lottery_type>')
def api_frequencies(lottery_type):
    """API endpoint for frequency data"""
    if lottery_type not in ['euromillones', 'loto_france']:
        return jsonify({'error': 'Invalid lottery type'}), 400
    
    try:
        years = request.args.get('years', app.config['DEFAULT_ANALYSIS_YEARS'], type=int)
        stats = LotteryStatistics(lottery_type)
        frequencies = stats.calculate_number_frequencies(years)
        return jsonify(frequencies)
    except Exception as e:
        logger.error(f"Error in frequencies API: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/patterns/<lottery_type>')
def api_patterns(lottery_type):
    """API endpoint for pattern analysis"""
    if lottery_type not in ['euromillones', 'loto_france']:
        return jsonify({'error': 'Invalid lottery type'}), 400
    
    try:
        years = request.args.get('years', app.config['DEFAULT_ANALYSIS_YEARS'], type=int)
        stats = LotteryStatistics(lottery_type)
        patterns = stats.analyze_patterns(years)
        return jsonify(patterns)
    except Exception as e:
        logger.error(f"Error in patterns API: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/settings')
def settings():
    """Settings page"""
    try:
        # Get current settings
        current_settings = {}
        all_settings = UserSettings.query.all()
        for setting in all_settings:
            current_settings[setting.setting_key] = setting.get_value()
        
        return render_template('settings.html', settings=current_settings)
    except Exception as e:
        logger.error(f"Error in settings route: {e}")
        return render_template('error.html', error=str(e))

@app.route('/save_settings', methods=['POST'])
def save_settings():
    """Save user settings"""
    try:
        settings_data = request.json

        for key, value in settings_data.items():
            setting = UserSettings.query.filter_by(setting_key=key).first()
            if setting:
                setting.setting_value = json.dumps(value) if not isinstance(value, str) else value
                setting.updated_at = datetime.utcnow()
            else:
                setting = UserSettings(key, value)
                db.session.add(setting)

        db.session.commit()
        return jsonify({'success': True, 'message': 'Settings saved successfully'})

    except Exception as e:
        db.session.rollback()
        logger.error(f"Error saving settings: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/settings')
def api_settings():
    """Get current settings"""
    try:
        settings = {}
        all_settings = UserSettings.query.all()
        for setting in all_settings:
            settings[setting.setting_key] = setting.get_value()

        # Add default values for missing settings
        defaults = {
            'euromillones_analysis_years': 5,
            'loto_france_analysis_years': 5,
            'default_combinations_count': 10,
            'neural_network_epochs': 100,
            'markov_chain_order': 3,
            'auto_update_enabled': True,
            'chart_theme': 'default',
            'items_per_page': 50,
            'show_tooltips': True,
            'animate_charts': True,
            'compact_mode': False,
            'language': 'es',
            'default_prediction_model': 'combined',
            'training_data_years': 5
        }

        for key, default_value in defaults.items():
            if key not in settings:
                settings[key] = default_value

        return jsonify(settings)
    except Exception as e:
        logger.error(f"Error getting settings: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/database_info')
def api_database_info():
    """Get database information"""
    try:
        euromillones_count = LotteryDraw.query.filter_by(lottery_type='euromillones').count()
        loto_france_count = LotteryDraw.query.filter_by(lottery_type='loto_france').count()
        predictions_count = PredictionResult.query.count()

        # Get database file size
        import os
        db_path = 'database/lottery.db'
        db_size = 'N/A'
        if os.path.exists(db_path):
            size_bytes = os.path.getsize(db_path)
            if size_bytes < 1024:
                db_size = f"{size_bytes} B"
            elif size_bytes < 1024 * 1024:
                db_size = f"{size_bytes / 1024:.1f} KB"
            else:
                db_size = f"{size_bytes / (1024 * 1024):.1f} MB"

        return jsonify({
            'euromillones_count': euromillones_count,
            'loto_france_count': loto_france_count,
            'predictions_count': predictions_count,
            'db_size': db_size
        })
    except Exception as e:
        logger.error(f"Error getting database info: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/train_models/<lottery_type>', methods=['POST'])
def train_models(lottery_type):
    """Train machine learning models"""
    if lottery_type not in ['euromillones', 'loto_france']:
        return jsonify({'error': 'Invalid lottery type'}), 400

    try:
        from ml_models import CombinedPredictor
        predictor = CombinedPredictor(lottery_type)
        results = predictor.train_all_models()

        return jsonify({
            'success': True,
            'message': 'Model training completed',
            'results': results
        })
    except Exception as e:
        logger.error(f"Error training models: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/visualizations/<lottery_type>')
def visualizations_page(lottery_type):
    """Advanced visualizations page"""
    if lottery_type not in ['euromillones', 'loto_france']:
        return redirect(url_for('index'))

    try:
        from visualizations import generate_all_visualizations
        from statistical_analysis import LotteryStatistics

        stats = LotteryStatistics(lottery_type)
        years = request.args.get('years', 5, type=int)

        # Get data for visualizations
        frequencies = stats.calculate_number_frequencies(years)
        patterns = stats.analyze_patterns(years)
        draws = stats.get_historical_data(years)

        # Generate visualizations
        visualizations = generate_all_visualizations(lottery_type, frequencies, patterns, draws)

        return render_template('visualizations.html',
                             lottery_type=lottery_type,
                             visualizations=visualizations,
                             years=years)
    except Exception as e:
        logger.error(f"Error in visualizations route: {e}")
        return render_template('error.html', error=str(e))

@app.route('/api/export_visualizations/<lottery_type>')
def export_visualizations(lottery_type):
    """Export visualizations as PDF"""
    if lottery_type not in ['euromillones', 'loto_france']:
        return jsonify({'error': 'Invalid lottery type'}), 400

    try:
        from visualizations import generate_all_visualizations, LotteryVisualizer
        from statistical_analysis import LotteryStatistics

        stats = LotteryStatistics(lottery_type)
        years = request.args.get('years', 5, type=int)

        # Get data
        frequencies = stats.calculate_number_frequencies(years)
        patterns = stats.analyze_patterns(years)
        draws = stats.get_historical_data(years)

        # Generate visualizations
        visualizations = generate_all_visualizations(lottery_type, frequencies, patterns, draws)

        # Export to PDF
        visualizer = LotteryVisualizer(lottery_type)
        filename = f"analisis_{lottery_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        filepath = os.path.join('uploads', filename)

        visualizer.export_to_pdf(visualizations, filepath)

        return jsonify({
            'success': True,
            'filename': filename,
            'download_url': f'/download/{filename}'
        })
    except Exception as e:
        logger.error(f"Error exporting visualizations: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/download/<filename>')
def download_file(filename):
    """Download exported files"""
    try:
        from flask import send_from_directory
        return send_from_directory('uploads', filename, as_attachment=True)
    except Exception as e:
        logger.error(f"Error downloading file: {e}")
        return jsonify({'error': 'File not found'}), 404

@app.route('/api/clear_predictions', methods=['POST'])
def clear_predictions():
    """Clear old predictions"""
    try:
        # Delete predictions older than 30 days
        cutoff_date = datetime.now() - timedelta(days=30)
        old_predictions = PredictionResult.query.filter(
            PredictionResult.created_at < cutoff_date
        ).all()

        count = len(old_predictions)
        for prediction in old_predictions:
            db.session.delete(prediction)

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'Deleted {count} old predictions',
            'deleted_count': count
        })
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error clearing predictions: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/export_database')
def export_database():
    """Export entire database as JSON"""
    try:
        from flask import Response
        import json

        # Get all data
        euromillones_draws = LotteryDraw.query.filter_by(lottery_type='euromillones').all()
        loto_france_draws = LotteryDraw.query.filter_by(lottery_type='loto_france').all()
        predictions = PredictionResult.query.all()
        settings = UserSettings.query.all()

        # Convert to dictionaries
        data = {
            'export_date': datetime.now().isoformat(),
            'euromillones_draws': [
                {
                    'id': draw.id,
                    'draw_date': draw.draw_date.isoformat(),
                    'main_numbers': draw.get_main_numbers(),
                    'additional_numbers': draw.get_additional_numbers(),
                    'jackpot_amount': draw.jackpot_amount,
                    'winners_count': draw.winners_count
                } for draw in euromillones_draws
            ],
            'loto_france_draws': [
                {
                    'id': draw.id,
                    'draw_date': draw.draw_date.isoformat(),
                    'main_numbers': draw.get_main_numbers(),
                    'additional_numbers': draw.get_additional_numbers(),
                    'jackpot_amount': draw.jackpot_amount,
                    'winners_count': draw.winners_count
                } for draw in loto_france_draws
            ],
            'predictions': [
                {
                    'id': prediction.id,
                    'lottery_type': prediction.lottery_type,
                    'prediction_date': prediction.prediction_date.isoformat(),
                    'main_numbers': prediction.get_main_numbers(),
                    'additional_numbers': prediction.get_additional_numbers(),
                    'probability_score': prediction.probability_score,
                    'model_used': prediction.model_used,
                    'created_at': prediction.created_at.isoformat()
                } for prediction in predictions
            ],
            'settings': [
                {
                    'setting_key': setting.setting_key,
                    'setting_value': setting.setting_value,
                    'updated_at': setting.updated_at.isoformat()
                } for setting in settings
            ]
        }

        filename = f"lottery_database_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        return Response(
            json.dumps(data, indent=2, ensure_ascii=False),
            mimetype='application/json',
            headers={'Content-Disposition': f'attachment; filename={filename}'}
        )
    except Exception as e:
        logger.error(f"Error exporting database: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/reset_settings', methods=['POST'])
def reset_settings():
    """Reset all settings to default values"""
    try:
        # Delete all current settings
        UserSettings.query.delete()

        # Add default settings
        default_settings = {
            'euromillones_analysis_years': 5,
            'loto_france_analysis_years': 5,
            'default_combinations_count': 10,
            'neural_network_epochs': 100,
            'markov_chain_order': 3,
            'auto_update_enabled': True,
            'chart_theme': 'default',
            'items_per_page': 50,
            'show_tooltips': True,
            'animate_charts': True,
            'compact_mode': False,
            'language': 'es',
            'default_prediction_model': 'combined',
            'training_data_years': 5
        }

        for key, value in default_settings.items():
            setting = UserSettings(key, value)
            db.session.add(setting)

        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Settings reset to default values'
        })
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error resetting settings: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/scrape_real_data', methods=['POST'])
def scrape_real_data():
    """Scrape real data from official sources"""
    try:
        lottery_type = request.json.get('lottery_type', 'both')
        max_results = request.json.get('max_results', 50)

        from real_scraper import RealLotteryScraper
        scraper = RealLotteryScraper()

        results = {'euromillones': [], 'loto_france': []}

        if lottery_type in ['euromillones', 'both']:
            logger.info("Scraping Euromillones data...")
            euro_results = scraper.scrape_euromillones_official(max_results)
            results['euromillones'] = euro_results

        if lottery_type in ['loto_france', 'both']:
            logger.info("Scraping Loto France data...")
            loto_results = scraper.scrape_loto_france_official(max_results)
            results['loto_france'] = loto_results

        total_scraped = len(results['euromillones']) + len(results['loto_france'])

        if total_scraped > 0:
            # Save to files for manual import
            import pandas as pd
            import os
            os.makedirs('scraped_data', exist_ok=True)

            if results['euromillones']:
                df_euro = pd.DataFrame(results['euromillones'])
                df_euro.to_csv('scraped_data/euromillones_real_scraped.csv', index=False)

            if results['loto_france']:
                df_loto = pd.DataFrame(results['loto_france'])
                df_loto.to_csv('scraped_data/loto_france_real_scraped.csv', index=False)

            return jsonify({
                'success': True,
                'message': f'Successfully scraped {total_scraped} real lottery results',
                'results': {
                    'euromillones_count': len(results['euromillones']),
                    'loto_france_count': len(results['loto_france']),
                    'total_count': total_scraped
                },
                'files_created': [
                    'scraped_data/euromillones_real_scraped.csv' if results['euromillones'] else None,
                    'scraped_data/loto_france_real_scraped.csv' if results['loto_france'] else None
                ]
            })
        else:
            return jsonify({
                'success': False,
                'message': 'No real data could be scraped from official sources. This may be due to website changes or anti-scraping measures.',
                'suggestion': 'Try downloading data manually from official sources and importing via CSV.'
            })

    except Exception as e:
        logger.error(f"Error scraping real data: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': 'Scraping failed. Official sites may have changed or have anti-scraping protection.'
        }), 500

@app.route('/api/clean_duplicates', methods=['POST'])
def clean_duplicates_api():
    """Clean duplicate lottery draws"""
    try:
        lottery_type = request.json.get('lottery_type', 'both')
        dry_run = request.json.get('dry_run', True)

        from clean_duplicates import clean_duplicates, clean_all_duplicates

        if lottery_type == 'both':
            removed_count = clean_all_duplicates(dry_run=dry_run)
        else:
            removed_count = clean_duplicates(lottery_type, dry_run=dry_run)

        return jsonify({
            'success': True,
            'message': f'{"Would remove" if dry_run else "Removed"} {removed_count} duplicate draws',
            'removed_count': removed_count,
            'dry_run': dry_run
        })

    except Exception as e:
        logger.error(f"Error cleaning duplicates: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/convert_format', methods=['POST'])
def convert_format_api():
    """Convert file format to standard format"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400

        file = request.files['file']
        lottery_type = request.form.get('lottery_type', 'euromillones')

        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        # Save uploaded file temporarily
        filename = secure_filename(file.filename)
        temp_path = os.path.join(app.config['UPLOAD_FOLDER'], f"temp_{filename}")
        file.save(temp_path)

        try:
            # Try custom format converter first (for your specific format)
            from custom_format_converter import convert_custom_lottery_file
            converted_path, df = convert_custom_lottery_file(temp_path, lottery_type)

            # Read a sample of the converted data
            sample_data = df.head(10).to_dict('records')

            return jsonify({
                'success': True,
                'message': f'Successfully converted {len(df)} rows using custom format converter',
                'converted_file': converted_path,
                'sample_data': sample_data,
                'total_rows': len(df),
                'format_detected': 'custom_format',
                'columns': list(df.columns)
            })

        except Exception as custom_error:
            logger.warning(f"Custom format conversion failed: {custom_error}")

            # Try general format converter
            try:
                from format_converter import convert_file_format
                converted_path, df = convert_file_format(temp_path, lottery_type)

                sample_data = df.head(10).to_dict('records')

                return jsonify({
                    'success': True,
                    'message': f'Successfully converted {len(df)} rows using general format converter',
                    'converted_file': converted_path,
                    'sample_data': sample_data,
                    'total_rows': len(df),
                    'format_detected': 'general_format',
                    'columns': list(df.columns)
                })

            except Exception as general_error:
                logger.error(f"General format conversion failed: {general_error}")

                return jsonify({
                    'success': False,
                    'error': 'Could not convert file format',
                    'custom_error': str(custom_error),
                    'general_error': str(general_error),
                    'suggestion': 'Please check that your file format matches one of the supported formats'
                }), 400

        finally:
            # Clean up temp file
            if os.path.exists(temp_path):
                os.remove(temp_path)

    except Exception as e:
        logger.error(f"Error in format conversion API: {e}")
        return jsonify({'error': str(e)}), 500

@app.errorhandler(404)
def not_found(error):
    return render_template('error.html', error='Page not found'), 404

@app.errorhandler(500)
def internal_error(error):
    return render_template('error.html', error='Internal server error'), 500

if __name__ == '__main__':
    app.run(debug=True, host='127.0.0.1', port=5000)
