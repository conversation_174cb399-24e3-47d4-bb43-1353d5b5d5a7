"""
Main Flask application for Lottery Analysis System
"""
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
import os
import json
from datetime import datetime, timedelta
import logging
from werkzeug.utils import secure_filename

# Import our modules
from config import config
from models import db, LotteryDraw, NumberFrequency, PredictionResult, UserSettings
from statistical_analysis import LotteryStatistics, analyze_all_lotteries
from ml_models import CombinedPredictor
from data_scraper import update_all_lottery_data
from data_importer import DataImporter

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_app(config_name='default'):
    """Create Flask application"""
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # Initialize database
    db.init_app(app)
    
    # Create database tables
    with app.app_context():
        # Create database directory if it doesn't exist
        os.makedirs('database', exist_ok=True)
        os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
        os.makedirs('logs', exist_ok=True)

        # Only create tables if we're not importing this module
        if __name__ == '__main__':
            db.create_all()
    
    return app

app = create_app()

@app.route('/')
def index():
    """Main dashboard"""
    try:
        # Get basic statistics
        euromillones_count = LotteryDraw.query.filter_by(lottery_type='euromillones').count()
        loto_france_count = LotteryDraw.query.filter_by(lottery_type='loto_france').count()
        
        # Get latest draws
        latest_euromillones = LotteryDraw.query.filter_by(lottery_type='euromillones').order_by(LotteryDraw.draw_date.desc()).first()
        latest_loto_france = LotteryDraw.query.filter_by(lottery_type='loto_france').order_by(LotteryDraw.draw_date.desc()).first()
        
        return render_template('index.html',
                             euromillones_count=euromillones_count,
                             loto_france_count=loto_france_count,
                             latest_euromillones=latest_euromillones,
                             latest_loto_france=latest_loto_france)
    except Exception as e:
        logger.error(f"Error in index route: {e}")
        return render_template('error.html', error=str(e))

@app.route('/lottery/<lottery_type>')
def lottery_analysis(lottery_type):
    """Lottery analysis page"""
    if lottery_type not in ['euromillones', 'loto_france']:
        return redirect(url_for('index'))

    try:
        stats = LotteryStatistics(lottery_type)

        # Get years parameter from URL or user settings
        years = request.args.get('years', type=int)
        if not years:
            years_setting = UserSettings.query.filter_by(setting_key=f'{lottery_type}_analysis_years').first()
            years = years_setting.get_value() if years_setting else app.config['DEFAULT_ANALYSIS_YEARS']

        # Calculate statistics
        frequencies = stats.calculate_number_frequencies(years)
        probabilities = stats.calculate_combination_probabilities()
        patterns = stats.analyze_patterns(years)

        return render_template('lottery_analysis.html',
                             lottery_type=lottery_type,
                             frequencies=frequencies,
                             probabilities=probabilities,
                             patterns=patterns,
                             years=years)
    except Exception as e:
        logger.error(f"Error in lottery analysis: {e}")
        return render_template('error.html', error=str(e))

@app.route('/predictions/<lottery_type>')
def predictions(lottery_type):
    """Predictions page"""
    if lottery_type not in ['euromillones', 'loto_france']:
        return redirect(url_for('index'))
    
    try:
        # Get recent predictions
        recent_predictions = PredictionResult.query.filter_by(
            lottery_type=lottery_type
        ).order_by(PredictionResult.created_at.desc()).limit(20).all()
        
        return render_template('predictions.html',
                             lottery_type=lottery_type,
                             predictions=recent_predictions)
    except Exception as e:
        logger.error(f"Error in predictions route: {e}")
        return render_template('error.html', error=str(e))

@app.route('/generate_predictions/<lottery_type>', methods=['POST'])
def generate_predictions(lottery_type):
    """Generate new predictions"""
    if lottery_type not in ['euromillones', 'loto_france']:
        return jsonify({'error': 'Invalid lottery type'}), 400
    
    try:
        # Get parameters from request
        num_combinations = request.json.get('num_combinations', 10)
        model_type = request.json.get('model_type', 'combined')
        
        # Create predictor and generate predictions
        predictor = CombinedPredictor(lottery_type)
        
        if model_type == 'combined':
            # Train models if needed
            training_results = predictor.train_all_models()
            predictions = predictor.generate_predictions(num_combinations)
        else:
            # Use specific model (simplified for now)
            predictions = predictor.generate_predictions(num_combinations)
        
        # Save predictions to database
        saved_count = predictor.save_predictions_to_db(predictions)
        
        return jsonify({
            'success': True,
            'message': f'Generated {len(predictions)} predictions',
            'saved_count': saved_count,
            'predictions': predictions
        })
        
    except Exception as e:
        logger.error(f"Error generating predictions: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/history/<lottery_type>')
def history(lottery_type):
    """Historical data page"""
    if lottery_type not in ['euromillones', 'loto_france']:
        return redirect(url_for('index'))
    
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 50
        
        # Get historical draws with pagination
        draws = LotteryDraw.query.filter_by(
            lottery_type=lottery_type
        ).order_by(LotteryDraw.draw_date.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return render_template('history.html',
                             lottery_type=lottery_type,
                             draws=draws)
    except Exception as e:
        logger.error(f"Error in history route: {e}")
        return render_template('error.html', error=str(e))

@app.route('/import_data')
def import_data_page():
    """Data import page"""
    return render_template('import_data.html')

@app.route('/upload_data', methods=['POST'])
def upload_data():
    """Handle file upload for data import"""
    try:
        if 'file' not in request.files:
            flash('No file selected', 'error')
            return redirect(url_for('import_data_page'))
        
        file = request.files['file']
        lottery_type = request.form.get('lottery_type')
        
        if file.filename == '':
            flash('No file selected', 'error')
            return redirect(url_for('import_data_page'))
        
        if lottery_type not in ['euromillones', 'loto_france']:
            flash('Invalid lottery type', 'error')
            return redirect(url_for('import_data_page'))
        
        # Save uploaded file
        filename = secure_filename(file.filename)
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(file_path)
        
        # Import data
        importer = DataImporter()
        result = importer.import_to_database(file_path, lottery_type)
        
        # Clean up uploaded file
        os.remove(file_path)
        
        if result['success']:
            flash(f"Successfully imported {result['saved_count']} draws. {result['duplicate_count']} duplicates skipped.", 'success')
        else:
            flash(f"Import failed: {result['message']}", 'error')
        
        if result['errors']:
            flash(f"Validation errors: {len(result['errors'])} rows had issues", 'warning')
        
        return redirect(url_for('import_data_page'))
        
    except Exception as e:
        logger.error(f"Error uploading data: {e}")
        flash(f"Upload failed: {str(e)}", 'error')
        return redirect(url_for('import_data_page'))

@app.route('/update_data')
def update_data():
    """Update lottery data from web sources"""
    try:
        saved_count = update_all_lottery_data()
        return jsonify({
            'success': True,
            'message': f'Updated data successfully. {saved_count} new draws added.',
            'saved_count': saved_count
        })
    except Exception as e:
        logger.error(f"Error updating data: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/frequencies/<lottery_type>')
def api_frequencies(lottery_type):
    """API endpoint for frequency data"""
    if lottery_type not in ['euromillones', 'loto_france']:
        return jsonify({'error': 'Invalid lottery type'}), 400
    
    try:
        years = request.args.get('years', app.config['DEFAULT_ANALYSIS_YEARS'], type=int)
        stats = LotteryStatistics(lottery_type)
        frequencies = stats.calculate_number_frequencies(years)
        return jsonify(frequencies)
    except Exception as e:
        logger.error(f"Error in frequencies API: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/patterns/<lottery_type>')
def api_patterns(lottery_type):
    """API endpoint for pattern analysis"""
    if lottery_type not in ['euromillones', 'loto_france']:
        return jsonify({'error': 'Invalid lottery type'}), 400
    
    try:
        years = request.args.get('years', app.config['DEFAULT_ANALYSIS_YEARS'], type=int)
        stats = LotteryStatistics(lottery_type)
        patterns = stats.analyze_patterns(years)
        return jsonify(patterns)
    except Exception as e:
        logger.error(f"Error in patterns API: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/settings')
def settings():
    """Settings page"""
    try:
        # Get current settings
        current_settings = {}
        all_settings = UserSettings.query.all()
        for setting in all_settings:
            current_settings[setting.setting_key] = setting.get_value()
        
        return render_template('settings.html', settings=current_settings)
    except Exception as e:
        logger.error(f"Error in settings route: {e}")
        return render_template('error.html', error=str(e))

@app.route('/save_settings', methods=['POST'])
def save_settings():
    """Save user settings"""
    try:
        settings_data = request.json

        for key, value in settings_data.items():
            setting = UserSettings.query.filter_by(setting_key=key).first()
            if setting:
                setting.setting_value = json.dumps(value) if not isinstance(value, str) else value
                setting.updated_at = datetime.utcnow()
            else:
                setting = UserSettings(key, value)
                db.session.add(setting)

        db.session.commit()
        return jsonify({'success': True, 'message': 'Settings saved successfully'})

    except Exception as e:
        db.session.rollback()
        logger.error(f"Error saving settings: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/settings')
def api_settings():
    """Get current settings"""
    try:
        settings = {}
        all_settings = UserSettings.query.all()
        for setting in all_settings:
            settings[setting.setting_key] = setting.get_value()

        # Add default values for missing settings
        defaults = {
            'euromillones_analysis_years': 5,
            'loto_france_analysis_years': 5,
            'default_combinations_count': 10,
            'neural_network_epochs': 100,
            'markov_chain_order': 3,
            'auto_update_enabled': True,
            'chart_theme': 'default',
            'items_per_page': 50,
            'show_tooltips': True,
            'animate_charts': True,
            'compact_mode': False,
            'language': 'es',
            'default_prediction_model': 'combined',
            'training_data_years': 5
        }

        for key, default_value in defaults.items():
            if key not in settings:
                settings[key] = default_value

        return jsonify(settings)
    except Exception as e:
        logger.error(f"Error getting settings: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/database_info')
def api_database_info():
    """Get database information"""
    try:
        euromillones_count = LotteryDraw.query.filter_by(lottery_type='euromillones').count()
        loto_france_count = LotteryDraw.query.filter_by(lottery_type='loto_france').count()
        predictions_count = PredictionResult.query.count()

        # Get database file size
        import os
        db_path = 'database/lottery.db'
        db_size = 'N/A'
        if os.path.exists(db_path):
            size_bytes = os.path.getsize(db_path)
            if size_bytes < 1024:
                db_size = f"{size_bytes} B"
            elif size_bytes < 1024 * 1024:
                db_size = f"{size_bytes / 1024:.1f} KB"
            else:
                db_size = f"{size_bytes / (1024 * 1024):.1f} MB"

        return jsonify({
            'euromillones_count': euromillones_count,
            'loto_france_count': loto_france_count,
            'predictions_count': predictions_count,
            'db_size': db_size
        })
    except Exception as e:
        logger.error(f"Error getting database info: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/train_models/<lottery_type>', methods=['POST'])
def train_models(lottery_type):
    """Train machine learning models"""
    if lottery_type not in ['euromillones', 'loto_france']:
        return jsonify({'error': 'Invalid lottery type'}), 400

    try:
        from ml_models import CombinedPredictor
        predictor = CombinedPredictor(lottery_type)
        results = predictor.train_all_models()

        return jsonify({
            'success': True,
            'message': 'Model training completed',
            'results': results
        })
    except Exception as e:
        logger.error(f"Error training models: {e}")
        return jsonify({'error': str(e)}), 500

@app.errorhandler(404)
def not_found(error):
    return render_template('error.html', error='Page not found'), 404

@app.errorhandler(500)
def internal_error(error):
    return render_template('error.html', error='Internal server error'), 500

if __name__ == '__main__':
    app.run(debug=True, host='127.0.0.1', port=5000)
