{% extends "base.html" %}

{% block title %}Predicciones - {{ lottery_type.title() }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1>
            <i class="fas fa-crystal-ball"></i> 
            Predicciones para {{ 'Euromillones' if lottery_type == 'euromillones' else 'Loto Francia' }}
        </h1>
        <p class="text-muted">
            Genera predicciones usando diferentes modelos matemáticos y estadísticos.
        </p>
    </div>
</div>

<!-- Prediction Generator -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-magic"></i> Generador de Predicciones
                </h5>
            </div>
            <div class="card-body">
                <form id="predictionForm">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="modelType" class="form-label">Modelo a Usar</label>
                            <select class="form-select" id="modelType" name="model_type">
                                <option value="combined">Modelo Combinado</option>
                                <option value="frequency">Solo Frecuencias</option>
                                <option value="markov">Cadenas de Markov</option>
                                <option value="neural">Red Neuronal</option>
                                <option value="random">Aleatorio</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="numCombinations" class="form-label">Número de Combinaciones</label>
                            <select class="form-select" id="numCombinations" name="num_combinations">
                                <option value="5">5 combinaciones</option>
                                <option value="10" selected>10 combinaciones</option>
                                <option value="15">15 combinaciones</option>
                                <option value="20">20 combinaciones</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="button" class="btn btn-primary w-100" onclick="generateNewPredictions()">
                                    <i class="fas fa-magic"></i> Generar Predicciones
                                </button>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="button" class="btn btn-outline-secondary w-100" onclick="trainModels()">
                                    <i class="fas fa-brain"></i> Entrenar Modelos
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Loading Indicator -->
<div id="loadingIndicator" class="row mb-4 loading">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Generando predicciones...</span>
                </div>
                <p class="mt-2">Generando predicciones, por favor espera...</p>
            </div>
        </div>
    </div>
</div>

<!-- Generated Predictions -->
<div id="newPredictions" class="row mb-4" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-sparkles"></i> Predicciones Generadas
                </h5>
            </div>
            <div class="card-body" id="newPredictionsContent">
                <!-- New predictions will be inserted here -->
            </div>
        </div>
    </div>
</div>

<!-- Recent Predictions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history"></i> Predicciones Recientes
                </h5>
            </div>
            <div class="card-body">
                {% if predictions %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Fecha</th>
                                    <th>Números Principales</th>
                                    <th>{{ 'Estrellas' if lottery_type == 'euromillones' else 'Chance' }}</th>
                                    <th>Modelo</th>
                                    <th>Puntuación</th>
                                    <th>Acciones</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for prediction in predictions %}
                                <tr>
                                    <td>{{ prediction.prediction_date.strftime('%d/%m/%Y') }}</td>
                                    <td>
                                        {% for number in prediction.get_main_numbers() %}
                                            <span class="number-ball">{{ number }}</span>
                                        {% endfor %}
                                    </td>
                                    <td>
                                        {% for number in prediction.get_additional_numbers() %}
                                            <span class="number-ball {{ 'star-ball' if lottery_type == 'euromillones' else 'chance-ball' }}">{{ number }}</span>
                                        {% endfor %}
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'primary' if prediction.model_used == 'combined' else 'secondary' }}">
                                            {{ prediction.model_used.title() }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="progress" style="width: 100px;">
                                            <div class="progress-bar" role="progressbar" 
                                                 style="width: {{ (prediction.probability_score * 100) | round(1) }}%">
                                                {{ (prediction.probability_score * 100) | round(1) }}%
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary" 
                                                onclick="copyPrediction({{ prediction.get_main_numbers() }}, {{ prediction.get_additional_numbers() }})">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-crystal-ball fa-3x text-muted mb-3"></i>
                        <h5>No hay predicciones disponibles</h5>
                        <p class="text-muted">Genera tu primera predicción usando el formulario de arriba.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Model Information -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle"></i> Información sobre los Modelos
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-chart-line"></i> Modelo de Frecuencias</h6>
                        <p class="small">
                            Utiliza análisis estadístico de frecuencias históricas para identificar 
                            números "calientes" y "fríos". Combina números frecuentes con menos frecuentes 
                            para crear combinaciones balanceadas.
                        </p>
                        
                        <h6><i class="fas fa-link"></i> Cadenas de Markov</h6>
                        <p class="small">
                            Modela las transiciones entre sorteos para predecir secuencias probables 
                            basándose en patrones históricos. Útil para identificar dependencias 
                            entre números consecutivos.
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-brain"></i> Red Neuronal</h6>
                        <p class="small">
                            Utiliza inteligencia artificial para identificar patrones complejos 
                            no lineales en los datos históricos. Puede detectar relaciones 
                            sutiles entre números y fechas.
                        </p>
                        
                        <h6><i class="fas fa-layer-group"></i> Modelo Combinado</h6>
                        <p class="small">
                            Fusiona los resultados de todos los modelos anteriores para crear 
                            predicciones más robustas. Utiliza un enfoque de ensemble para 
                            mejorar la precisión general.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Training Status -->
<div id="trainingStatus" class="row mb-4" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-cogs"></i> Estado del Entrenamiento
                </h5>
            </div>
            <div class="card-body" id="trainingStatusContent">
                <!-- Training status will be inserted here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    const lotteryType = "{{ lottery_type }}";

    function generateNewPredictions() {
        const modelType = document.getElementById('modelType').value;
        const numCombinations = document.getElementById('numCombinations').value;
        
        // Show loading indicator
        document.getElementById('loadingIndicator').style.display = 'block';
        document.getElementById('newPredictions').style.display = 'none';
        
        const requestData = {
            model_type: modelType,
            num_combinations: parseInt(numCombinations)
        };
        
        fetch(`/generate_predictions/${lotteryType}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestData)
        })
        .then(response => response.json())
        .then(data => {
            // Hide loading indicator
            document.getElementById('loadingIndicator').style.display = 'none';
            
            if (data.error) {
                showAlert('danger', 'Error: ' + data.error);
                return;
            }
            
            // Display new predictions
            displayNewPredictions(data.predictions);
            showAlert('success', data.message);
        })
        .catch(error => {
            document.getElementById('loadingIndicator').style.display = 'none';
            showAlert('danger', 'Error de conexión: ' + error.message);
        });
    }

    function displayNewPredictions(predictions) {
        const container = document.getElementById('newPredictionsContent');
        let html = '<div class="row">';
        
        predictions.forEach((prediction, index) => {
            const mainNumbers = prediction.main_numbers || prediction.numbers;
            const additionalNumbers = prediction.additional_numbers || prediction.stars || prediction.chance;
            const probability = prediction.probability || prediction.probability_score || 0;
            const model = prediction.model || prediction.model_used || 'unknown';
            
            html += `
                <div class="col-md-6 mb-3">
                    <div class="card border-primary">
                        <div class="card-header">
                            <h6 class="mb-0">Predicción ${index + 1}</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-2">
                                <strong>Números principales:</strong><br>
                                ${mainNumbers.map(num => `<span class="number-ball">${num}</span>`).join('')}
                            </div>
                            <div class="mb-2">
                                <strong>${lotteryType === 'euromillones' ? 'Estrellas' : 'Chance'}:</strong><br>
                                ${additionalNumbers.map(num => `<span class="number-ball ${lotteryType === 'euromillones' ? 'star-ball' : 'chance-ball'}">${num}</span>`).join('')}
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">
                                    Modelo: <span class="badge bg-secondary">${model}</span>
                                    Puntuación: ${(probability * 100).toFixed(1)}%
                                </small>
                            </div>
                            <button class="btn btn-sm btn-outline-primary" 
                                    onclick="copyPrediction([${mainNumbers.join(',')}], [${additionalNumbers.join(',')}])">
                                <i class="fas fa-copy"></i> Copiar
                            </button>
                        </div>
                    </div>
                </div>
            `;
        });
        
        html += '</div>';
        container.innerHTML = html;
        document.getElementById('newPredictions').style.display = 'block';
    }

    function trainModels() {
        document.getElementById('trainingStatus').style.display = 'block';
        document.getElementById('trainingStatusContent').innerHTML = `
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Entrenando modelos...</span>
                </div>
                <p class="mt-2">Entrenando modelos de machine learning, esto puede tomar unos minutos...</p>
            </div>
        `;
        
        fetch(`/train_models/${lotteryType}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                document.getElementById('trainingStatusContent').innerHTML = `
                    <div class="alert alert-danger">
                        <h6>Error en el entrenamiento</h6>
                        <p>${data.error}</p>
                    </div>
                `;
                return;
            }
            
            let statusHtml = '<div class="row">';
            for (const [model, success] of Object.entries(data.results)) {
                statusHtml += `
                    <div class="col-md-4 mb-2">
                        <div class="alert alert-${success ? 'success' : 'warning'}">
                            <strong>${model.charAt(0).toUpperCase() + model.slice(1)}</strong><br>
                            ${success ? '✓ Entrenado correctamente' : '⚠ Error en entrenamiento'}
                        </div>
                    </div>
                `;
            }
            statusHtml += '</div>';
            
            document.getElementById('trainingStatusContent').innerHTML = statusHtml;
            showAlert('success', 'Entrenamiento completado');
        })
        .catch(error => {
            document.getElementById('trainingStatusContent').innerHTML = `
                <div class="alert alert-danger">
                    <h6>Error de conexión</h6>
                    <p>${error.message}</p>
                </div>
            `;
        });
    }

    function copyPrediction(mainNumbers, additionalNumbers) {
        const text = `${mainNumbers.join(', ')} | ${additionalNumbers.join(', ')}`;
        navigator.clipboard.writeText(text).then(() => {
            showAlert('success', 'Predicción copiada al portapapeles');
        }).catch(() => {
            showAlert('warning', 'No se pudo copiar al portapapeles');
        });
    }
</script>
{% endblock %}
