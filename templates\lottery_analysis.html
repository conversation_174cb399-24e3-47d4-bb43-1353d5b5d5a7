{% extends "base.html" %}

{% block title %}Análisis - {{ lottery_type.title() }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1>
            <i class="fas fa-chart-bar"></i> 
            <PERSON><PERSON><PERSON><PERSON> de {{ 'Euromillones' if lottery_type == 'euromillones' else 'Loto Francia' }}
        </h1>
        <p class="text-muted">
            Análisis estadístico completo basado en {{ frequencies.total_draws }} sorteos de los últimos {{ years }} años.
        </p>
    </div>
</div>

<!-- Statistics Overview -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-primary">{{ frequencies.total_draws }}</h3>
                <p class="text-muted">Total Sorteos</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-success">{{ frequencies.date_range.from }}</h3>
                <p class="text-muted">Desde</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-info">{{ frequencies.date_range.to }}</h3>
                <p class="text-muted">Hasta</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-warning">{{ years }}</h3>
                <p class="text-muted">Años Analizados</p>
            </div>
        </div>
    </div>
</div>

<!-- Hot and Cold Numbers -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">
                    <i class="fas fa-fire"></i> Números Más Frecuentes (Calientes)
                </h5>
            </div>
            <div class="card-body">
                {% set sorted_main = frequencies.main_numbers.items() | sort(attribute='1.frequency', reverse=true) %}
                {% for number, data in sorted_main[:15] %}
                    <span class="number-ball hot-number" 
                          title="Frecuencia: {{ data.frequency }} ({{ "%.1f"|format(data.percentage) }}%)">
                        {{ number }}
                    </span>
                {% endfor %}
                <div class="mt-3">
                    <small class="text-muted">
                        Los 15 números que más han salido en los sorteos analizados.
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-snowflake"></i> Números Menos Frecuentes (Fríos)
                </h5>
            </div>
            <div class="card-body">
                {% set sorted_main = frequencies.main_numbers.items() | sort(attribute='1.frequency') %}
                {% for number, data in sorted_main[:15] %}
                    <span class="number-ball cold-number" 
                          title="Frecuencia: {{ data.frequency }} ({{ "%.1f"|format(data.percentage) }}%)">
                        {{ number }}
                    </span>
                {% endfor %}
                <div class="mt-3">
                    <small class="text-muted">
                        Los 15 números que menos han salido en los sorteos analizados.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Frequency Charts -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-column"></i> Frecuencia de Números Principales
                </h5>
            </div>
            <div class="card-body">
                <canvas id="mainNumbersChart" height="80"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-column"></i> 
                    Frecuencia de {{ 'Estrellas' if lottery_type == 'euromillones' else 'Números Chance' }}
                </h5>
            </div>
            <div class="card-body">
                <canvas id="additionalNumbersChart" height="80"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Pattern Analysis -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card pattern-card">
            <div class="card-body text-center">
                <h3>{{ "%.1f"|format(patterns.consecutive_stats.average) }}</h3>
                <p class="mb-0">Promedio de Números Consecutivos</p>
                <small>Máximo: {{ patterns.consecutive_stats.max }}</small>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card pattern-card">
            <div class="card-body text-center">
                <h3>{{ "%.1f"|format(patterns.sum_stats.average) }}</h3>
                <p class="mb-0">Suma Promedio de Números</p>
                <small>Rango: {{ patterns.sum_stats.min }} - {{ patterns.sum_stats.max }}</small>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card pattern-card">
            <div class="card-body text-center">
                <h3>{{ patterns.even_odd_stats.most_common_distribution[0][0] }}</h3>
                <p class="mb-0">Distribución Par/Impar Más Común</p>
                <small>{{ patterns.even_odd_stats.most_common_distribution[0][1] }} veces</small>
            </div>
        </div>
    </div>
</div>

<!-- Probability Analysis -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-calculator"></i> Análisis de Probabilidades
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for prize, data in probabilities.items() %}
                    <div class="col-md-4 mb-3">
                        <div class="card border-primary">
                            <div class="card-body text-center">
                                <h6 class="card-title">{{ data.description }}</h6>
                                <h4 class="text-primary">{{ data.odds }}</h4>
                                <small class="text-muted">
                                    Probabilidad: {{ "%.2e"|format(data.probability) }}
                                </small>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Most Common Pairs -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-link"></i> Parejas de Números Más Frecuentes
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for pair, count in patterns.number_pairs.most_common(12) %}
                    <div class="col-md-2 mb-2 text-center">
                        <div class="border rounded p-2">
                            <span class="number-ball">{{ pair[0] }}</span>
                            <span class="number-ball">{{ pair[1] }}</span>
                            <br>
                            <small class="text-muted">{{ count }} veces</small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Configuration Panel -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cog"></i> Configuración del Análisis
                </h5>
            </div>
            <div class="card-body">
                <form id="analysisForm">
                    <div class="row">
                        <div class="col-md-4">
                            <label for="analysisYears" class="form-label">Años a Analizar</label>
                            <select class="form-select" id="analysisYears" name="years">
                                <option value="1" {{ 'selected' if years == 1 }}>1 año</option>
                                <option value="2" {{ 'selected' if years == 2 }}>2 años</option>
                                <option value="5" {{ 'selected' if years == 5 }}>5 años</option>
                                <option value="10" {{ 'selected' if years == 10 }}>10 años</option>
                                <option value="15" {{ 'selected' if years == 15 }}>15 años</option>
                                <option value="20" {{ 'selected' if years == 20 }}>20 años</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="button" class="btn btn-primary" onclick="updateAnalysis()">
                                    <i class="fas fa-sync-alt"></i> Actualizar Análisis
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="button" class="btn btn-success" onclick="generatePredictions()">
                                    <i class="fas fa-magic"></i> Generar Predicciones
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Chart data
    const mainNumbersData = {{ frequencies.main_numbers | tojson }};
    const additionalNumbersData = {{ frequencies.additional_numbers | tojson }};
    const lotteryType = "{{ lottery_type }}";

    // Prepare data for charts
    const mainLabels = Object.keys(mainNumbersData);
    const mainFrequencies = mainLabels.map(num => mainNumbersData[num].frequency);
    
    const additionalLabels = Object.keys(additionalNumbersData);
    const additionalFrequencies = additionalLabels.map(num => additionalNumbersData[num].frequency);

    // Main numbers chart
    const mainCtx = document.getElementById('mainNumbersChart').getContext('2d');
    new Chart(mainCtx, {
        type: 'bar',
        data: {
            labels: mainLabels,
            datasets: [{
                label: 'Frecuencia',
                data: mainFrequencies,
                backgroundColor: mainFrequencies.map(freq => {
                    const max = Math.max(...mainFrequencies);
                    const min = Math.min(...mainFrequencies);
                    const ratio = (freq - min) / (max - min);
                    return `rgba(${255 - ratio * 100}, ${100 + ratio * 100}, 234, 0.8)`;
                }),
                borderColor: 'rgba(102, 126, 234, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        afterLabel: function(context) {
                            const num = context.label;
                            const data = mainNumbersData[num];
                            return [
                                `Porcentaje: ${data.percentage.toFixed(1)}%`,
                                `Último sorteo: ${data.last_drawn || 'Nunca'}`,
                                `Días desde último: ${data.days_since_last || 'N/A'}`
                            ];
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Frecuencia'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Números'
                    }
                }
            }
        }
    });

    // Additional numbers chart
    const additionalCtx = document.getElementById('additionalNumbersChart').getContext('2d');
    new Chart(additionalCtx, {
        type: 'bar',
        data: {
            labels: additionalLabels,
            datasets: [{
                label: 'Frecuencia',
                data: additionalFrequencies,
                backgroundColor: lotteryType === 'euromillones' ? 'rgba(240, 147, 251, 0.8)' : 'rgba(79, 172, 254, 0.8)',
                borderColor: lotteryType === 'euromillones' ? 'rgba(240, 147, 251, 1)' : 'rgba(79, 172, 254, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        afterLabel: function(context) {
                            const num = context.label;
                            const data = additionalNumbersData[num];
                            return [
                                `Porcentaje: ${data.percentage.toFixed(1)}%`,
                                `Último sorteo: ${data.last_drawn || 'Nunca'}`,
                                `Días desde último: ${data.days_since_last || 'N/A'}`
                            ];
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Frecuencia'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: lotteryType === 'euromillones' ? 'Estrellas' : 'Números Chance'
                    }
                }
            }
        }
    });

    function updateAnalysis() {
        const years = document.getElementById('analysisYears').value;
        window.location.href = `/lottery/${lotteryType}?years=${years}`;
    }

    function generatePredictions() {
        window.location.href = `/predictions/${lotteryType}`;
    }
</script>
{% endblock %}
