"""
Statistical analysis module for lottery data
"""
import pandas as pd
import numpy as np
from collections import Counter, defaultdict
from datetime import datetime, timedelta
import math
from models import db, LotteryDraw, NumberFrequency
from config import Config
import logging

logger = logging.getLogger(__name__)

class LotteryStatistics:
    """Core statistical analysis for lottery data"""
    
    def __init__(self, lottery_type):
        self.lottery_type = lottery_type
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
    
    def get_historical_data(self, years=None):
        """Get historical lottery data from database"""
        if years is None:
            years = Config.DEFAULT_ANALYSIS_YEARS
        
        cutoff_date = datetime.now().date() - timedelta(days=years * 365)
        
        draws = LotteryDraw.query.filter(
            LotteryDraw.lottery_type == self.lottery_type,
            LotteryDraw.draw_date >= cutoff_date
        ).order_by(LotteryDraw.draw_date.desc()).all()
        
        return draws
    
    def calculate_number_frequencies(self, years=None):
        """Calculate frequency statistics for all numbers"""
        draws = self.get_historical_data(years)
        
        if not draws:
            return {}
        
        # Initialize counters
        main_freq = Counter()
        additional_freq = Counter()
        main_last_drawn = {}
        additional_last_drawn = {}
        
        # Count frequencies and track last drawn dates
        for draw in draws:
            main_numbers = draw.get_main_numbers()
            additional_numbers = draw.get_additional_numbers()
            
            for num in main_numbers:
                main_freq[num] += 1
                if num not in main_last_drawn or draw.draw_date > main_last_drawn[num]:
                    main_last_drawn[num] = draw.draw_date
            
            for num in additional_numbers:
                additional_freq[num] += 1
                if num not in additional_last_drawn or draw.draw_date > additional_last_drawn[num]:
                    additional_last_drawn[num] = draw.draw_date
        
        # Calculate days since last drawn
        today = datetime.now().date()
        
        # Prepare results
        results = {
            'main_numbers': {},
            'additional_numbers': {},
            'total_draws': len(draws),
            'date_range': {
                'from': draws[-1].draw_date.isoformat() if draws else None,
                'to': draws[0].draw_date.isoformat() if draws else None
            }
        }
        
        # Main numbers statistics
        for num in range(self.config['main_numbers']['min'], self.config['main_numbers']['max'] + 1):
            frequency = main_freq.get(num, 0)
            last_drawn = main_last_drawn.get(num)
            days_since = (today - last_drawn).days if last_drawn else None
            
            results['main_numbers'][num] = {
                'frequency': frequency,
                'percentage': (frequency / len(draws) * 100) if draws else 0,
                'last_drawn': last_drawn.isoformat() if last_drawn else None,
                'days_since_last': days_since
            }
        
        # Additional numbers statistics
        additional_config = self.config.get('stars', self.config.get('chance'))
        for num in range(additional_config['min'], additional_config['max'] + 1):
            frequency = additional_freq.get(num, 0)
            last_drawn = additional_last_drawn.get(num)
            days_since = (today - last_drawn).days if last_drawn else None
            
            results['additional_numbers'][num] = {
                'frequency': frequency,
                'percentage': (frequency / len(draws) * 100) if draws else 0,
                'last_drawn': last_drawn.isoformat() if last_drawn else None,
                'days_since_last': days_since
            }
        
        return results
    
    def calculate_combination_probabilities(self):
        """Calculate theoretical probabilities for different prize tiers"""
        main_config = self.config['main_numbers']
        additional_config = self.config.get('stars', self.config.get('chance'))
        
        def combination(n, r):
            """Calculate nCr (combinations)"""
            if r > n or r < 0:
                return 0
            return math.factorial(n) // (math.factorial(r) * math.factorial(n - r))
        
        # Total possible combinations
        main_combinations = combination(main_config['max'], main_config['count'])
        additional_combinations = combination(additional_config['max'], additional_config['count'])
        total_combinations = main_combinations * additional_combinations
        
        # Calculate probabilities for different prize tiers
        probabilities = {}
        
        # Jackpot (all main + all additional)
        probabilities['jackpot'] = {
            'description': f"{main_config['count']} main + {additional_config['count']} additional",
            'probability': 1 / total_combinations,
            'odds': f"1 in {total_combinations:,}"
        }
        
        # Second prize (all main + partial additional)
        if additional_config['count'] > 1:
            second_additional = combination(additional_config['count'], additional_config['count'] - 1) * \
                              combination(additional_config['max'] - additional_config['count'], 1)
            probabilities['second'] = {
                'description': f"{main_config['count']} main + {additional_config['count'] - 1} additional",
                'probability': second_additional / total_combinations,
                'odds': f"1 in {total_combinations // second_additional:,}"
            }
        
        # Third prize (partial main + all additional)
        third_main = combination(main_config['count'], main_config['count'] - 1) * \
                    combination(main_config['max'] - main_config['count'], 1)
        probabilities['third'] = {
            'description': f"{main_config['count'] - 1} main + {additional_config['count']} additional",
            'probability': (third_main * additional_combinations) / total_combinations,
            'odds': f"1 in {total_combinations // (third_main * additional_combinations):,}"
        }
        
        return probabilities
    
    def analyze_patterns(self, years=None):
        """Analyze patterns in lottery draws"""
        draws = self.get_historical_data(years)
        
        if not draws:
            return {}
        
        patterns = {
            'consecutive_numbers': [],
            'even_odd_distribution': [],
            'sum_ranges': [],
            'number_pairs': Counter(),
            'hot_cold_analysis': {}
        }
        
        for draw in draws:
            main_numbers = sorted(draw.get_main_numbers())
            
            # Consecutive numbers
            consecutive_count = 0
            for i in range(len(main_numbers) - 1):
                if main_numbers[i + 1] - main_numbers[i] == 1:
                    consecutive_count += 1
            patterns['consecutive_numbers'].append(consecutive_count)
            
            # Even/odd distribution
            even_count = sum(1 for num in main_numbers if num % 2 == 0)
            patterns['even_odd_distribution'].append({
                'even': even_count,
                'odd': len(main_numbers) - even_count
            })
            
            # Sum of numbers
            number_sum = sum(main_numbers)
            patterns['sum_ranges'].append(number_sum)
            
            # Number pairs
            for i in range(len(main_numbers)):
                for j in range(i + 1, len(main_numbers)):
                    pair = tuple(sorted([main_numbers[i], main_numbers[j]]))
                    patterns['number_pairs'][pair] += 1
        
        # Analyze hot and cold numbers
        frequencies = self.calculate_number_frequencies(years)
        main_freqs = [(num, data['frequency']) for num, data in frequencies['main_numbers'].items()]
        main_freqs.sort(key=lambda x: x[1], reverse=True)
        
        patterns['hot_cold_analysis'] = {
            'hottest_numbers': main_freqs[:10],
            'coldest_numbers': main_freqs[-10:],
            'average_frequency': sum(freq for _, freq in main_freqs) / len(main_freqs)
        }
        
        # Statistical summaries
        patterns['consecutive_stats'] = {
            'average': np.mean(patterns['consecutive_numbers']),
            'max': max(patterns['consecutive_numbers']),
            'distribution': Counter(patterns['consecutive_numbers'])
        }
        
        patterns['sum_stats'] = {
            'average': np.mean(patterns['sum_ranges']),
            'min': min(patterns['sum_ranges']),
            'max': max(patterns['sum_ranges']),
            'std': np.std(patterns['sum_ranges'])
        }
        
        patterns['even_odd_stats'] = {
            'most_common_distribution': Counter(
                f"{dist['even']}E-{dist['odd']}O" for dist in patterns['even_odd_distribution']
            ).most_common(5)
        }
        
        return patterns
    
    def update_frequency_cache(self):
        """Update the frequency cache in database"""
        frequencies = self.calculate_number_frequencies()
        
        # Clear existing frequencies for this lottery
        NumberFrequency.query.filter_by(lottery_type=self.lottery_type).delete()
        
        # Add main numbers
        for num, data in frequencies['main_numbers'].items():
            freq_record = NumberFrequency(
                lottery_type=self.lottery_type,
                number_type='main',
                number=num,
                frequency=data['frequency'],
                last_drawn=datetime.fromisoformat(data['last_drawn']).date() if data['last_drawn'] else None,
                days_since_last=data['days_since_last'] or 0
            )
            db.session.add(freq_record)
        
        # Add additional numbers
        for num, data in frequencies['additional_numbers'].items():
            freq_record = NumberFrequency(
                lottery_type=self.lottery_type,
                number_type='additional',
                number=num,
                frequency=data['frequency'],
                last_drawn=datetime.fromisoformat(data['last_drawn']).date() if data['last_drawn'] else None,
                days_since_last=data['days_since_last'] or 0
            )
            db.session.add(freq_record)
        
        try:
            db.session.commit()
            logger.info(f"Updated frequency cache for {self.lottery_type}")
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error updating frequency cache: {e}")

def analyze_all_lotteries():
    """Run analysis for all supported lotteries"""
    results = {}
    
    for lottery_type in ['euromillones', 'loto_france']:
        try:
            analyzer = LotteryStatistics(lottery_type)
            results[lottery_type] = {
                'frequencies': analyzer.calculate_number_frequencies(),
                'probabilities': analyzer.calculate_combination_probabilities(),
                'patterns': analyzer.analyze_patterns()
            }
            analyzer.update_frequency_cache()
        except Exception as e:
            logger.error(f"Error analyzing {lottery_type}: {e}")
            results[lottery_type] = {'error': str(e)}
    
    return results
