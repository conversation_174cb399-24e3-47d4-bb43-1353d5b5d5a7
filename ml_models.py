"""
Machine Learning models for lottery prediction
"""
import numpy as np
import pandas as pd
from sklearn.neural_network import MLPRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from collections import defaultdict
import random
from datetime import datetime
import logging
from models import LotteryDraw, PredictionResult, db
from statistical_analysis import LotteryStatistics
from config import Config

# Try to import TensorFlow, but make it optional
try:
    import tensorflow as tf
    from tensorflow import keras
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False
    print("Warning: TensorFlow not available. Neural network predictions will use scikit-learn instead.")

logger = logging.getLogger(__name__)

class MarkovChainPredictor:
    """Markov Chain model for lottery number prediction"""
    
    def __init__(self, lottery_type, order=3):
        self.lottery_type = lottery_type
        self.order = order  # Order of the Markov chain
        self.transition_matrix = defaultdict(lambda: defaultdict(int))
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
        self.is_trained = False
    
    def prepare_sequences(self, draws):
        """Prepare number sequences for Markov chain training"""
        sequences = []
        
        for draw in draws:
            main_numbers = sorted(draw.get_main_numbers())
            sequences.append(main_numbers)
        
        return sequences
    
    def train(self, years=5):
        """Train the Markov chain model"""
        stats = LotteryStatistics(self.lottery_type)
        draws = stats.get_historical_data(years)
        
        if len(draws) < self.order + 1:
            logger.warning(f"Not enough data to train Markov chain (need at least {self.order + 1} draws)")
            return False
        
        sequences = self.prepare_sequences(draws)
        
        # Build transition matrix
        for sequence in sequences:
            for i in range(len(sequence) - self.order):
                state = tuple(sequence[i:i + self.order])
                next_number = sequence[i + self.order]
                self.transition_matrix[state][next_number] += 1
        
        # Normalize probabilities
        for state in self.transition_matrix:
            total = sum(self.transition_matrix[state].values())
            for next_number in self.transition_matrix[state]:
                self.transition_matrix[state][next_number] /= total
        
        self.is_trained = True
        logger.info(f"Markov chain trained with {len(sequences)} sequences")
        return True
    
    def predict_numbers(self, num_predictions=5):
        """Generate number predictions using Markov chain"""
        if not self.is_trained:
            logger.error("Model not trained")
            return []
        
        predictions = []
        main_config = self.config['main_numbers']
        
        for _ in range(num_predictions):
            # Start with a random state from the transition matrix
            if not self.transition_matrix:
                continue
            
            state = random.choice(list(self.transition_matrix.keys()))
            predicted_numbers = list(state)
            
            # Generate remaining numbers
            while len(predicted_numbers) < main_config['count']:
                if state in self.transition_matrix:
                    # Choose next number based on probabilities
                    candidates = list(self.transition_matrix[state].keys())
                    probabilities = list(self.transition_matrix[state].values())
                    
                    if candidates:
                        next_number = np.random.choice(candidates, p=probabilities)
                        if next_number not in predicted_numbers:
                            predicted_numbers.append(next_number)
                            # Update state
                            state = tuple(predicted_numbers[-self.order:])
                        else:
                            # If number already selected, choose randomly
                            available = [n for n in range(main_config['min'], main_config['max'] + 1) 
                                       if n not in predicted_numbers]
                            if available:
                                predicted_numbers.append(random.choice(available))
                    else:
                        # Fallback to random selection
                        available = [n for n in range(main_config['min'], main_config['max'] + 1) 
                                   if n not in predicted_numbers]
                        if available:
                            predicted_numbers.append(random.choice(available))
                else:
                    # State not found, use random
                    available = [n for n in range(main_config['min'], main_config['max'] + 1) 
                               if n not in predicted_numbers]
                    if available:
                        predicted_numbers.append(random.choice(available))
            
            if len(predicted_numbers) == main_config['count']:
                predictions.append(sorted(predicted_numbers))
        
        return predictions

class NeuralNetworkPredictor:
    """Neural Network model for lottery prediction"""
    
    def __init__(self, lottery_type):
        self.lottery_type = lottery_type
        self.model = None
        self.scaler = StandardScaler()
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
        self.is_trained = False
    
    def prepare_features(self, draws):
        """Prepare features for neural network training"""
        features = []
        targets = []
        
        for i in range(len(draws) - 1):
            current_draw = draws[i]
            next_draw = draws[i + 1]
            
            # Features: current draw numbers + statistical features
            current_numbers = current_draw.get_main_numbers()
            
            # Create feature vector
            feature_vector = []
            
            # One-hot encoding for current numbers
            main_config = self.config['main_numbers']
            for num in range(main_config['min'], main_config['max'] + 1):
                feature_vector.append(1 if num in current_numbers else 0)
            
            # Statistical features
            feature_vector.extend([
                sum(current_numbers),  # Sum of numbers
                max(current_numbers) - min(current_numbers),  # Range
                len([n for n in current_numbers if n % 2 == 0]),  # Even count
                current_draw.draw_date.weekday(),  # Day of week
                current_draw.draw_date.month,  # Month
            ])
            
            features.append(feature_vector)
            
            # Target: next draw numbers (one-hot encoded)
            next_numbers = next_draw.get_main_numbers()
            target_vector = []
            for num in range(main_config['min'], main_config['max'] + 1):
                target_vector.append(1 if num in next_numbers else 0)
            
            targets.append(target_vector)
        
        return np.array(features), np.array(targets)
    
    def build_model(self, input_dim, output_dim):
        """Build neural network model"""
        if TENSORFLOW_AVAILABLE:
            model = keras.Sequential([
                keras.layers.Dense(128, activation='relu', input_shape=(input_dim,)),
                keras.layers.Dropout(0.3),
                keras.layers.Dense(64, activation='relu'),
                keras.layers.Dropout(0.3),
                keras.layers.Dense(32, activation='relu'),
                keras.layers.Dense(output_dim, activation='sigmoid')
            ])

            model.compile(
                optimizer='adam',
                loss='binary_crossentropy',
                metrics=['accuracy']
            )

            return model
        else:
            # Use scikit-learn MLPRegressor as fallback
            model = MLPRegressor(
                hidden_layer_sizes=(128, 64, 32),
                activation='relu',
                solver='adam',
                max_iter=200,
                random_state=42
            )
            return model
    
    def train(self, years=5):
        """Train the neural network model"""
        stats = LotteryStatistics(self.lottery_type)
        draws = stats.get_historical_data(years)
        
        if len(draws) < 50:
            logger.warning("Not enough data to train neural network")
            return False
        
        # Reverse to get chronological order
        draws = draws[::-1]
        
        X, y = self.prepare_features(draws)
        
        if len(X) == 0:
            logger.error("No features prepared for training")
            return False
        
        # Scale features
        X_scaled = self.scaler.fit_transform(X)
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X_scaled, y, test_size=0.2, random_state=42
        )
        
        # Build and train model
        self.model = self.build_model(X.shape[1], y.shape[1])

        if TENSORFLOW_AVAILABLE:
            # Train with TensorFlow/Keras
            early_stopping = keras.callbacks.EarlyStopping(
                monitor='val_loss', patience=10, restore_best_weights=True
            )

            history = self.model.fit(
                X_train, y_train,
                epochs=Config.NEURAL_NETWORK_EPOCHS,
                batch_size=32,
                validation_data=(X_test, y_test),
                callbacks=[early_stopping],
                verbose=0
            )
        else:
            # Train with scikit-learn
            # For multi-output, we'll train on the sum of targets as a regression problem
            y_train_sum = np.sum(y_train, axis=1)
            self.model.fit(X_train, y_train_sum)
        
        self.is_trained = True
        logger.info(f"Neural network trained with {len(X)} samples")
        return True
    
    def predict_numbers(self, num_predictions=5):
        """Generate predictions using neural network"""
        if not self.is_trained or self.model is None:
            logger.error("Model not trained")
            return []
        
        predictions = []
        main_config = self.config['main_numbers']
        
        # Get recent draw for context
        stats = LotteryStatistics(self.lottery_type)
        recent_draws = stats.get_historical_data(1)
        
        if not recent_draws:
            return []
        
        latest_draw = recent_draws[0]
        
        for _ in range(num_predictions):
            # Prepare feature vector based on latest draw
            current_numbers = latest_draw.get_main_numbers()
            feature_vector = []
            
            # One-hot encoding
            for num in range(main_config['min'], main_config['max'] + 1):
                feature_vector.append(1 if num in current_numbers else 0)
            
            # Statistical features
            feature_vector.extend([
                sum(current_numbers),
                max(current_numbers) - min(current_numbers),
                len([n for n in current_numbers if n % 2 == 0]),
                latest_draw.draw_date.weekday(),
                latest_draw.draw_date.month,
            ])
            
            # Scale and predict
            X_pred = self.scaler.transform([feature_vector])

            if TENSORFLOW_AVAILABLE:
                probabilities = self.model.predict(X_pred, verbose=0)[0]
                # Select top numbers based on probabilities
                number_probs = [(i + main_config['min'], prob)
                              for i, prob in enumerate(probabilities[:main_config['max']])]
                number_probs.sort(key=lambda x: x[1], reverse=True)
                predicted_numbers = [num for num, _ in number_probs[:main_config['count']]]
            else:
                # For scikit-learn, use a simpler approach
                prediction_score = self.model.predict(X_pred)[0]
                # Generate numbers based on the prediction score
                all_numbers = list(range(main_config['min'], main_config['max'] + 1))
                # Add some randomness based on the prediction
                random.shuffle(all_numbers)
                predicted_numbers = sorted(all_numbers[:main_config['count']])

            predictions.append(predicted_numbers)
        
        return predictions

class CombinedPredictor:
    """Combined predictor using multiple models"""
    
    def __init__(self, lottery_type):
        self.lottery_type = lottery_type
        self.markov_predictor = MarkovChainPredictor(lottery_type)
        self.neural_predictor = NeuralNetworkPredictor(lottery_type)
        self.stats = LotteryStatistics(lottery_type)
        self.config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
    
    def train_all_models(self, years=5):
        """Train all prediction models"""
        results = {}
        
        # Train Markov chain
        try:
            results['markov'] = self.markov_predictor.train(years)
        except Exception as e:
            logger.error(f"Error training Markov chain: {e}")
            results['markov'] = False
        
        # Train neural network
        try:
            results['neural'] = self.neural_predictor.train(years)
        except Exception as e:
            logger.error(f"Error training neural network: {e}")
            results['neural'] = False
        
        return results
    
    def generate_predictions(self, num_combinations=10):
        """Generate combined predictions"""
        all_predictions = []
        
        # Get frequency-based predictions
        frequencies = self.stats.calculate_number_frequencies()
        freq_predictions = self._generate_frequency_predictions(frequencies, num_combinations // 3)
        
        # Get Markov chain predictions
        if self.markov_predictor.is_trained:
            markov_predictions = self.markov_predictor.predict_numbers(num_combinations // 3)
        else:
            markov_predictions = []
        
        # Get neural network predictions
        if self.neural_predictor.is_trained:
            neural_predictions = self.neural_predictor.predict_numbers(num_combinations // 3)
        else:
            neural_predictions = []
        
        # Combine predictions
        for pred in freq_predictions:
            all_predictions.append({
                'main_numbers': pred,
                'model': 'frequency',
                'probability': self._calculate_frequency_probability(pred, frequencies)
            })
        
        for pred in markov_predictions:
            all_predictions.append({
                'main_numbers': pred,
                'model': 'markov',
                'probability': 0.5  # Placeholder probability
            })
        
        for pred in neural_predictions:
            all_predictions.append({
                'main_numbers': pred,
                'model': 'neural',
                'probability': 0.5  # Placeholder probability
            })
        
        # Sort by probability and return top combinations
        all_predictions.sort(key=lambda x: x['probability'], reverse=True)
        return all_predictions[:num_combinations]
    
    def _generate_frequency_predictions(self, frequencies, count):
        """Generate predictions based on frequency analysis"""
        predictions = []
        main_config = self.config['main_numbers']
        
        # Get hot and cold numbers
        main_freqs = [(num, data['frequency']) for num, data in frequencies['main_numbers'].items()]
        main_freqs.sort(key=lambda x: x[1], reverse=True)
        
        hot_numbers = [num for num, _ in main_freqs[:15]]
        cold_numbers = [num for num, _ in main_freqs[-15:]]
        
        for _ in range(count):
            # Mix hot and cold numbers
            selected = []
            
            # Select some hot numbers
            hot_count = random.randint(2, 4)
            selected.extend(random.sample(hot_numbers, min(hot_count, len(hot_numbers))))
            
            # Fill with random numbers
            remaining = main_config['count'] - len(selected)
            available = [n for n in range(main_config['min'], main_config['max'] + 1) 
                        if n not in selected]
            selected.extend(random.sample(available, min(remaining, len(available))))
            
            if len(selected) == main_config['count']:
                predictions.append(sorted(selected))
        
        return predictions
    
    def _calculate_frequency_probability(self, numbers, frequencies):
        """Calculate probability score based on frequencies"""
        total_score = 0
        for num in numbers:
            freq_data = frequencies['main_numbers'].get(num, {'percentage': 0})
            total_score += freq_data['percentage']

        return total_score / len(numbers) / 100  # Normalize to 0-1 range

    def save_predictions_to_db(self, predictions):
        """Save predictions to database"""
        saved_count = 0
        prediction_date = datetime.now().date()

        # Generate additional numbers for each prediction
        additional_config = self.config.get('stars', self.config.get('chance'))

        for pred in predictions:
            try:
                # Generate random additional numbers (simplified approach)
                additional_numbers = random.sample(
                    range(additional_config['min'], additional_config['max'] + 1),
                    additional_config['count']
                )

                prediction = PredictionResult(
                    lottery_type=self.lottery_type,
                    prediction_date=prediction_date,
                    main_numbers=pred['main_numbers'],
                    additional_numbers=additional_numbers,
                    probability_score=pred['probability'],
                    model_used=pred['model']
                )

                db.session.add(prediction)
                saved_count += 1

            except Exception as e:
                logger.error(f"Error saving prediction: {e}")
                continue

        try:
            db.session.commit()
            logger.info(f"Saved {saved_count} predictions for {self.lottery_type}")
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error committing predictions: {e}")

        return saved_count
