#!/usr/bin/env python3
"""
Database initialization script for Lottery Analysis System
"""
import os
import sys
from datetime import datetime, timedelta
import random

# Add current directory to path to import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import db, LotteryDraw, UserSettings
from config import Config

def create_sample_data():
    """Create sample lottery data for testing"""
    print("Creating sample data...")
    
    # Sample Euromillones data
    euromillones_data = [
        {'date': '2024-01-02', 'main': [5, 12, 23, 34, 45], 'additional': [3, 8]},
        {'date': '2024-01-05', 'main': [8, 15, 27, 38, 42], 'additional': [1, 11]},
        {'date': '2024-01-09', 'main': [2, 19, 31, 41, 47], 'additional': [4, 9]},
        {'date': '2024-01-12', 'main': [7, 14, 25, 36, 49], 'additional': [2, 10]},
        {'date': '2024-01-16', 'main': [11, 18, 29, 40, 44], 'additional': [5, 7]},
        {'date': '2024-01-19', 'main': [3, 16, 22, 33, 46], 'additional': [6, 12]},
        {'date': '2024-01-23', 'main': [9, 17, 24, 35, 48], 'additional': [1, 4]},
        {'date': '2024-01-26', 'main': [6, 13, 26, 37, 43], 'additional': [8, 11]},
        {'date': '2024-01-30', 'main': [4, 20, 28, 39, 50], 'additional': [2, 9]},
        {'date': '2024-02-02', 'main': [10, 21, 30, 32, 41], 'additional': [3, 7]},
    ]
    
    # Sample Loto France data
    loto_france_data = [
        {'date': '2024-01-01', 'main': [5, 12, 23, 34, 45], 'additional': [3]},
        {'date': '2024-01-03', 'main': [8, 15, 27, 38, 42], 'additional': [7]},
        {'date': '2024-01-06', 'main': [2, 19, 31, 41, 47], 'additional': [2]},
        {'date': '2024-01-08', 'main': [7, 14, 25, 36, 49], 'additional': [9]},
        {'date': '2024-01-10', 'main': [11, 18, 29, 40, 44], 'additional': [5]},
        {'date': '2024-01-13', 'main': [3, 16, 22, 33, 46], 'additional': [6]},
        {'date': '2024-01-15', 'main': [9, 17, 24, 35, 48], 'additional': [1]},
        {'date': '2024-01-17', 'main': [6, 13, 26, 37, 43], 'additional': [8]},
        {'date': '2024-01-20', 'main': [4, 20, 28, 39, 45], 'additional': [4]},
        {'date': '2024-01-22', 'main': [10, 21, 30, 32, 41], 'additional': [10]},
    ]
    
    # Add Euromillones data
    for data in euromillones_data:
        draw = LotteryDraw(
            lottery_type='euromillones',
            draw_date=datetime.strptime(data['date'], '%Y-%m-%d').date(),
            main_numbers=data['main'],
            additional_numbers=data['additional'],
            jackpot_amount=random.randint(15000000, 100000000),
            winners_count=random.randint(0, 3)
        )
        db.session.add(draw)
    
    # Add Loto France data
    for data in loto_france_data:
        draw = LotteryDraw(
            lottery_type='loto_france',
            draw_date=datetime.strptime(data['date'], '%Y-%m-%d').date(),
            main_numbers=data['main'],
            additional_numbers=data['additional'],
            jackpot_amount=random.randint(2000000, 15000000),
            winners_count=random.randint(0, 5)
        )
        db.session.add(draw)
    
    print(f"Added {len(euromillones_data)} Euromillones draws")
    print(f"Added {len(loto_france_data)} Loto France draws")

def create_default_settings():
    """Create default user settings"""
    print("Creating default settings...")
    
    default_settings = {
        'euromillones_analysis_years': 5,
        'loto_france_analysis_years': 5,
        'default_combinations_count': 10,
        'neural_network_epochs': 50,
        'markov_chain_order': 3,
        'auto_update_enabled': True,
        'chart_theme': 'default'
    }
    
    for key, value in default_settings.items():
        setting = UserSettings(key, value)
        db.session.add(setting)
    
    print(f"Added {len(default_settings)} default settings")

def init_database():
    """Initialize the database with tables and sample data"""
    print("Initializing Lottery Analysis System Database...")
    print("=" * 50)
    
    # Create Flask app
    app = create_app()
    
    with app.app_context():
        # Create database directory first
        os.makedirs('database', exist_ok=True)
        os.makedirs('uploads', exist_ok=True)
        os.makedirs('logs', exist_ok=True)

        # Create all tables
        print("Creating database tables...")
        db.create_all()
        print("✓ Database tables created successfully")
        
        # Check if data already exists
        existing_draws = LotteryDraw.query.count()
        if existing_draws > 0:
            print(f"Database already contains {existing_draws} draws")
            response = input("Do you want to add sample data anyway? (y/N): ")
            if response.lower() != 'y':
                print("Skipping sample data creation")
                return
        
        try:
            # Create sample data
            create_sample_data()
            
            # Create default settings
            create_default_settings()
            
            # Commit all changes
            db.session.commit()
            print("✓ Sample data created successfully")
            
        except Exception as e:
            db.session.rollback()
            print(f"✗ Error creating sample data: {e}")
            return
    
    print("=" * 50)
    print("Database initialization completed!")
    print("\nNext steps:")
    print("1. Run 'python app.py' to start the web server")
    print("2. Open http://127.0.0.1:5000 in your browser")
    print("3. Click 'Actualizar Datos' to fetch real lottery data")
    print("4. Explore the analysis and prediction features")
    print("\nNote: The sample data is for testing purposes only.")

if __name__ == '__main__':
    init_database()
