{% extends "base.html" %}

{% block title %}Importar Datos - Sistema de Análisis de Loterías{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1>
            <i class="fas fa-upload"></i> Importar Datos Históricos
        </h1>
        <p class="text-muted">
            Importa datos históricos de sorteos desde archivos CSV, TXT o XLSX.
        </p>

        <!-- Data Status Alert -->
        <div class="alert alert-info alert-dismissible fade show" role="alert">
            <h6><i class="fas fa-info-circle"></i> Estado Actual de los Datos</h6>
            <p class="mb-2">
                El sistema actualmente contiene <strong id="currentDataCount">cargando...</strong> sorteos.
                Puedes importar datos adicionales desde archivos oficiales.
            </p>
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-sm btn-outline-info" onclick="checkCurrentData()">
                    <i class="fas fa-refresh"></i> Actualizar Estado
                </button>
                <button type="button" class="btn btn-sm btn-outline-warning" onclick="showDataSources()">
                    <i class="fas fa-globe"></i> Fuentes Oficiales
                </button>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-file-upload"></i> Subir Archivo
                </h5>
            </div>
            <div class="card-body">
                <form action="{{ url_for('upload_data') }}" method="post" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="lottery_type" class="form-label">Tipo de Lotería</label>
                        <select class="form-select" id="lottery_type" name="lottery_type" required>
                            <option value="">Selecciona una lotería</option>
                            <option value="euromillones">Euromillones</option>
                            <option value="loto_france">Loto Francia</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="file" class="form-label">Archivo de Datos</label>
                        <input type="file" class="form-control" id="file" name="file" 
                               accept=".csv,.txt,.xlsx" required>
                        <div class="form-text">
                            Formatos soportados: CSV, TXT, XLSX (máximo 16MB)
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload"></i> Importar Datos
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle"></i> Formato de Datos
                </h5>
            </div>
            <div class="card-body">
                <h6>Euromillones</h6>
                <p class="small">
                    Debe contener: fecha, 5 números principales (1-50), 2 estrellas (1-12)
                </p>
                
                <h6>Loto Francia</h6>
                <p class="small">
                    Debe contener: fecha, 5 números principales (1-49), 1 número Chance (1-10)
                </p>
                
                <h6>Ejemplo CSV:</h6>
                <pre class="small bg-light p-2">
fecha,num1,num2,num3,num4,num5,star1,star2
2024-01-01,5,12,23,34,45,3,8
2024-01-05,8,15,27,38,42,1,11
                </pre>
                
                <div class="alert alert-warning small">
                    <i class="fas fa-exclamation-triangle"></i>
                    El sistema validará automáticamente los datos y reportará errores.
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-download"></i> Datos de Ejemplo
                </h5>
            </div>
            <div class="card-body">
                <p class="small">Descarga archivos de ejemplo para ver el formato correcto:</p>
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary btn-sm" onclick="downloadExample('euromillones')">
                        <i class="fas fa-download"></i> Ejemplo Euromillones
                    </button>
                    <button class="btn btn-outline-info btn-sm" onclick="downloadExample('loto_france')">
                        <i class="fas fa-download"></i> Ejemplo Loto Francia
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list"></i> Instrucciones Detalladas
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Preparación del Archivo</h6>
                        <ol>
                            <li>Asegúrate de que el archivo tenga encabezados de columna</li>
                            <li>Las fechas deben estar en formato DD/MM/YYYY o YYYY-MM-DD</li>
                            <li>Los números deben ser enteros válidos</li>
                            <li>No debe haber filas vacías o datos faltantes</li>
                        </ol>
                    </div>
                    <div class="col-md-6">
                        <h6>Validaciones Automáticas</h6>
                        <ul>
                            <li>Formato de fecha válido</li>
                            <li>Números dentro del rango permitido</li>
                            <li>Cantidad correcta de números por sorteo</li>
                            <li>Detección de duplicados</li>
                        </ul>
                    </div>
                </div>
                
                <div class="alert alert-info mt-3">
                    <h6><i class="fas fa-lightbulb"></i> Consejos</h6>
                    <ul class="mb-0">
                        <li>Si tienes datos en formato diferente, puedes usar Excel para reorganizar las columnas</li>
                        <li>El sistema detectará automáticamente separadores en archivos TXT</li>
                        <li>Los sorteos duplicados serán omitidos automáticamente</li>
                        <li>Recibirás un reporte detallado de la importación</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Format Converter Section -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-magic"></i> Convertidor de Formatos
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info mb-3">
                        <h6><i class="fas fa-info-circle"></i> ¿Tu archivo tiene un formato diferente?</h6>
                        <p class="mb-2">
                            Si tu archivo CSV tiene un formato como:
                            <code>30/05/2025,04,07,14,33,36,,01,05</code>
                        </p>
                        <p class="mb-0">
                            Usa nuestro convertidor automático para transformarlo al formato estándar.
                        </p>
                    </div>

                    <button type="button" class="btn btn-info" onclick="showFormatConverter()">
                        <i class="fas fa-magic"></i> Abrir Convertidor de Formatos
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Data Management Section -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0">
                        <i class="fas fa-cogs"></i> Gestión de Datos
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning mb-3">
                        <h6><i class="fas fa-exclamation-triangle"></i> Herramientas Avanzadas</h6>
                        <p class="mb-0">
                            Utiliza estas herramientas para gestionar los datos existentes en el sistema.
                        </p>
                    </div>

                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-info w-100" onclick="checkCurrentData()">
                                <i class="fas fa-info-circle"></i> Estado de Datos
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-success w-100" onclick="scrapeRealData()">
                                <i class="fas fa-globe"></i> Obtener Datos Reales
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-warning w-100" onclick="cleanDuplicates()">
                                <i class="fas fa-broom"></i> Limpiar Duplicados
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-danger w-100" onclick="showClearDataOptions()">
                                <i class="fas fa-trash-alt"></i> Eliminar Datos
                            </button>
                        </div>
                    </div>

                    <div class="row mt-2">
                        <div class="col-md-6 mb-2">
                            <button class="btn btn-outline-secondary w-100" onclick="createBackup()">
                                <i class="fas fa-download"></i> Crear Backup
                            </button>
                        </div>
                        <div class="col-md-6 mb-2">
                            <button class="btn btn-outline-primary w-100" onclick="showDataSources()">
                                <i class="fas fa-external-link-alt"></i> Fuentes Oficiales
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function downloadExample(lotteryType) {
    let csvContent = '';

    if (lotteryType === 'euromillones') {
        csvContent = `date,num1,num2,num3,num4,num5,star1,star2,jackpot,winners
2025-06-01,5,12,23,34,45,3,8,50000000,1
2025-06-03,8,15,27,38,42,1,11,60000000,0
2025-06-06,2,19,31,41,47,4,9,70000000,2
2025-06-10,7,14,25,36,49,2,10,80000000,1
2025-06-13,11,18,29,40,44,5,7,90000000,0`;
    } else {
        csvContent = `date,num1,num2,num3,num4,num5,chance,jackpot,winners
2025-06-01,5,12,23,34,45,3,5000000,1
2025-06-02,8,15,27,38,42,7,6000000,0
2025-06-04,2,19,31,41,47,2,7000000,2
2025-06-07,7,14,25,36,49,9,8000000,1
2025-06-09,11,18,29,40,44,5,9000000,0`;
    }

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `ejemplo_${lotteryType}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

function checkCurrentData() {
    fetch('/api/data_status')
        .then(response => response.json())
        .then(data => {
            const total = data.total_draws;
            const euroCount = data.euromillones.count;
            const lotoCount = data.loto_france.count;

            document.getElementById('currentDataCount').innerHTML =
                `<strong>${total}</strong> (${euroCount} Euromillones + ${lotoCount} Loto Francia)`;

            showAlert('success', `Estado actualizado: ${total} sorteos en total`);
        })
        .catch(error => {
            showAlert('danger', 'Error al verificar el estado: ' + error.message);
        });
}

function showDataSources() {
    const modal = `
        <div class="modal fade" id="dataSourcesModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-warning text-dark">
                        <h5 class="modal-title">
                            <i class="fas fa-globe"></i> Fuentes Oficiales de Datos
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-primary text-white">
                                        <h6 class="mb-0">🌟 Euromillones</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="list-group list-group-flush">
                                            <a href="https://www.loteriasyapuestas.es/es/euromillones/resultados" target="_blank" class="list-group-item list-group-item-action">
                                                <strong>Loterías y Apuestas del Estado</strong><br>
                                                <small class="text-muted">España - Oficial</small>
                                            </a>
                                            <a href="https://www.euro-millions.com/results" target="_blank" class="list-group-item list-group-item-action">
                                                <strong>Euro-millions.com</strong><br>
                                                <small class="text-muted">Internacional</small>
                                            </a>
                                            <a href="https://www.fdj.fr/jeux/jeux-de-tirage/euromillions" target="_blank" class="list-group-item list-group-item-action">
                                                <strong>FDJ</strong><br>
                                                <small class="text-muted">Francia - Oficial</small>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0">🍀 Loto Francia</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="list-group list-group-flush">
                                            <a href="https://www.fdj.fr/jeux/jeux-de-tirage/loto/resultats-loto" target="_blank" class="list-group-item list-group-item-action">
                                                <strong>FDJ - Française des Jeux</strong><br>
                                                <small class="text-muted">Francia - Oficial</small>
                                            </a>
                                            <a href="https://www.loto.fr/resultats" target="_blank" class="list-group-item list-group-item-action">
                                                <strong>Loto.fr</strong><br>
                                                <small class="text-muted">Francia</small>
                                            </a>
                                            <a href="https://www.tirage-gagnant.com/loto/" target="_blank" class="list-group-item list-group-item-action">
                                                <strong>Tirage Gagnant</strong><br>
                                                <small class="text-muted">Francia</small>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info mt-3">
                            <h6><i class="fas fa-info-circle"></i> Cómo Usar</h6>
                            <ol class="mb-0">
                                <li>Visita las fuentes oficiales</li>
                                <li>Descarga o copia los datos históricos</li>
                                <li>Organiza en formato CSV según los ejemplos</li>
                                <li>Importa usando el formulario de esta página</li>
                            </ol>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modal);
    const modalElement = new bootstrap.Modal(document.getElementById('dataSourcesModal'));
    modalElement.show();

    // Clean up modal after hiding
    document.getElementById('dataSourcesModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

// Utility function to show alerts
function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Find a container to add the alert
    const container = document.querySelector('.container') || document.body;
    container.insertBefore(alertDiv, container.firstChild);

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Load current data status on page load
document.addEventListener('DOMContentLoaded', function() {
    checkCurrentData();
});

function scrapeRealData() {
    const modal = `
        <div class="modal fade" id="scrapeDataModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-success text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-globe"></i> Obtener Datos Reales Automáticamente
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle"></i> Importante</h6>
                            <p>Esta función intentará obtener datos reales desde fuentes oficiales mediante web scraping automático.</p>
                        </div>

                        <div class="mb-3">
                            <label for="scrapeType" class="form-label">Seleccionar lotería:</label>
                            <select class="form-select" id="scrapeType">
                                <option value="both">Ambas loterías</option>
                                <option value="euromillones">Solo Euromillones</option>
                                <option value="loto_france">Solo Loto Francia</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="maxResults" class="form-label">Máximo de resultados:</label>
                            <select class="form-select" id="maxResults">
                                <option value="25">25 sorteos</option>
                                <option value="50" selected>50 sorteos</option>
                                <option value="100">100 sorteos</option>
                            </select>
                        </div>

                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> Nota</h6>
                            <p class="mb-0">
                                El scraping puede fallar si los sitios web han cambiado su estructura o tienen protección anti-scraping.
                                En ese caso, usa la importación manual con archivos CSV.
                            </p>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="button" class="btn btn-success" onclick="executeScraping()">
                            <i class="fas fa-download"></i> Iniciar Scraping
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modal);
    const modalElement = new bootstrap.Modal(document.getElementById('scrapeDataModal'));
    modalElement.show();

    // Clean up modal after hiding
    document.getElementById('scrapeDataModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function executeScraping() {
    const lotteryType = document.getElementById('scrapeType').value;
    const maxResults = parseInt(document.getElementById('maxResults').value);
    const modal = bootstrap.Modal.getInstance(document.getElementById('scrapeDataModal'));

    // Show loading state
    const modalBody = document.querySelector('#scrapeDataModal .modal-body');
    modalBody.innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-success" role="status">
                <span class="visually-hidden">Scraping...</span>
            </div>
            <p class="mt-3">Obteniendo datos reales desde fuentes oficiales...</p>
            <p class="text-muted">Esto puede tomar varios minutos. Por favor, espera.</p>
        </div>
    `;

    // Disable close buttons
    document.querySelector('#scrapeDataModal .btn-close').disabled = true;
    document.querySelector('#scrapeDataModal .modal-footer').style.display = 'none';

    // Make request
    fetch('/api/scrape_real_data', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            lottery_type: lotteryType,
            max_results: maxResults
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            modalBody.innerHTML = `
                <div class="text-center">
                    <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                    <h5 class="text-success">¡Datos Obtenidos Exitosamente!</h5>
                    <p>Se obtuvieron <strong>${data.results.total_count}</strong> sorteos reales:</p>
                    <ul class="list-unstyled">
                        <li>📊 Euromillones: ${data.results.euromillones_count} sorteos</li>
                        <li>📊 Loto Francia: ${data.results.loto_france_count} sorteos</li>
                    </ul>
                    <div class="alert alert-info">
                        <small>Los datos se han guardado en archivos CSV. Puedes importarlos usando el formulario de esta página.</small>
                    </div>
                </div>
            `;
        } else {
            modalBody.innerHTML = `
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle text-warning fa-3x mb-3"></i>
                    <h5 class="text-warning">No se Pudieron Obtener Datos Reales</h5>
                    <p>${data.message}</p>
                    ${data.suggestion ? `<div class="alert alert-info"><small>${data.suggestion}</small></div>` : ''}
                    <div class="alert alert-secondary">
                        <h6>Alternativas:</h6>
                        <ul class="text-start">
                            <li>Descargar datos manualmente desde fuentes oficiales</li>
                            <li>Usar los archivos de ejemplo proporcionados</li>
                            <li>Importar archivos CSV existentes</li>
                        </ul>
                    </div>
                </div>
            `;
        }

        // Show close button
        document.querySelector('#scrapeDataModal .modal-footer').innerHTML = `
            <button type="button" class="btn btn-primary" data-bs-dismiss="modal">
                <i class="fas fa-check"></i> Continuar
            </button>
        `;
        document.querySelector('#scrapeDataModal .modal-footer').style.display = 'block';
    })
    .catch(error => {
        modalBody.innerHTML = `
            <div class="text-center">
                <i class="fas fa-exclamation-triangle text-danger fa-3x mb-3"></i>
                <h5 class="text-danger">Error de Conexión</h5>
                <p>No se pudo conectar con el servidor de scraping.</p>
                <p class="text-muted">Error: ${error.message}</p>
            </div>
        `;

        document.querySelector('#scrapeDataModal .modal-footer').innerHTML = `
            <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Cerrar</button>
        `;
        document.querySelector('#scrapeDataModal .modal-footer').style.display = 'block';
    });
}

function cleanDuplicates() {
    const modal = `
        <div class="modal fade" id="cleanDuplicatesModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-warning text-dark">
                        <h5 class="modal-title">
                            <i class="fas fa-broom"></i> Limpiar Duplicados
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> ¿Qué hace esta función?</h6>
                            <p>Detecta y elimina sorteos duplicados basándose en la fecha. Si hay múltiples sorteos para la misma fecha, mantiene el más reciente.</p>
                        </div>

                        <div class="mb-3">
                            <label for="cleanType" class="form-label">Seleccionar lotería:</label>
                            <select class="form-select" id="cleanType">
                                <option value="both">Ambas loterías</option>
                                <option value="euromillones">Solo Euromillones</option>
                                <option value="loto_france">Solo Loto Francia</option>
                            </select>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="dryRun" checked>
                            <label class="form-check-label" for="dryRun">
                                <strong>Modo de prueba</strong> (solo mostrar qué se eliminaría, sin borrar realmente)
                            </label>
                        </div>

                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle"></i> Precaución</h6>
                            <p class="mb-0">
                                Si desmarcas "Modo de prueba", los duplicados se eliminarán permanentemente.
                                Recomendamos hacer primero una prueba para ver qué se eliminaría.
                            </p>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="button" class="btn btn-warning" onclick="executeCleanDuplicates()">
                            <i class="fas fa-broom"></i> Limpiar Duplicados
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modal);
    const modalElement = new bootstrap.Modal(document.getElementById('cleanDuplicatesModal'));
    modalElement.show();

    // Clean up modal after hiding
    document.getElementById('cleanDuplicatesModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function executeCleanDuplicates() {
    const lotteryType = document.getElementById('cleanType').value;
    const dryRun = document.getElementById('dryRun').checked;
    const modal = bootstrap.Modal.getInstance(document.getElementById('cleanDuplicatesModal'));

    // Show loading state
    const modalBody = document.querySelector('#cleanDuplicatesModal .modal-body');
    modalBody.innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-warning" role="status">
                <span class="visually-hidden">Limpiando...</span>
            </div>
            <p class="mt-3">${dryRun ? 'Analizando duplicados...' : 'Eliminando duplicados...'}</p>
        </div>
    `;

    // Disable close buttons
    document.querySelector('#cleanDuplicatesModal .btn-close').disabled = true;
    document.querySelector('#cleanDuplicatesModal .modal-footer').style.display = 'none';

    // Make request
    fetch('/api/clean_duplicates', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            lottery_type: lotteryType,
            dry_run: dryRun
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            modalBody.innerHTML = `
                <div class="text-center">
                    <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                    <h5 class="text-success">${dryRun ? 'Análisis Completado' : 'Limpieza Completada'}</h5>
                    <p>${data.message}</p>
                    ${data.removed_count > 0 ? `
                        <div class="alert alert-${dryRun ? 'info' : 'success'}">
                            <strong>${data.removed_count}</strong> duplicados ${dryRun ? 'encontrados' : 'eliminados'}
                        </div>
                    ` : `
                        <div class="alert alert-success">
                            <i class="fas fa-thumbs-up"></i> No se encontraron duplicados
                        </div>
                    `}
                </div>
            `;
        } else {
            modalBody.innerHTML = `
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle text-danger fa-3x mb-3"></i>
                    <h5 class="text-danger">Error en la Limpieza</h5>
                    <p>Error: ${data.error}</p>
                </div>
            `;
        }

        // Show close button
        document.querySelector('#cleanDuplicatesModal .modal-footer').innerHTML = `
            <button type="button" class="btn btn-primary" data-bs-dismiss="modal" onclick="checkCurrentData()">
                <i class="fas fa-check"></i> Continuar
            </button>
        `;
        document.querySelector('#cleanDuplicatesModal .modal-footer').style.display = 'block';
    })
    .catch(error => {
        modalBody.innerHTML = `
            <div class="text-center">
                <i class="fas fa-exclamation-triangle text-danger fa-3x mb-3"></i>
                <h5 class="text-danger">Error de Conexión</h5>
                <p>Error: ${error.message}</p>
            </div>
        `;

        document.querySelector('#cleanDuplicatesModal .modal-footer').innerHTML = `
            <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Cerrar</button>
        `;
        document.querySelector('#cleanDuplicatesModal .modal-footer').style.display = 'block';
    });
}

function showFormatConverter() {
    const modal = `
        <div class="modal fade" id="formatConverterModal" tabindex="-1">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header bg-info text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-magic"></i> Convertidor de Formatos
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> Formatos Soportados</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Tu formato actual:</h6>
                                    <code>30/05/2025,04,07,14,33,36,,01,05</code>
                                    <small class="d-block text-muted">Fecha, 5 números principales, separador vacío, 2 estrellas</small>
                                </div>
                                <div class="col-md-6">
                                    <h6>Formato estándar:</h6>
                                    <code>date,num1,num2,num3,num4,num5,star1,star2</code>
                                    <small class="d-block text-muted">Con headers y columnas organizadas</small>
                                </div>
                            </div>
                        </div>

                        <form id="formatConverterForm" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="convertFile" class="form-label">Seleccionar archivo a convertir:</label>
                                <input type="file" class="form-control" id="convertFile" name="file" accept=".csv,.txt" required>
                                <div class="form-text">
                                    <i class="fas fa-info-circle"></i>
                                    Soporta archivos CSV y TXT con tu formato específico
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="convertLotteryType" class="form-label">Tipo de lotería:</label>
                                <select class="form-select" id="convertLotteryType" name="lottery_type">
                                    <option value="euromillones">Euromillones</option>
                                    <option value="loto_france">Loto Francia</option>
                                </select>
                            </div>

                            <div id="conversionResult" class="mt-3" style="display: none;"></div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="button" class="btn btn-info" onclick="executeFormatConversion()">
                            <i class="fas fa-magic"></i> Convertir Formato
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modal);
    const modalElement = new bootstrap.Modal(document.getElementById('formatConverterModal'));
    modalElement.show();

    // Clean up modal after hiding
    document.getElementById('formatConverterModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function executeFormatConversion() {
    const fileInput = document.getElementById('convertFile');
    const lotteryType = document.getElementById('convertLotteryType').value;

    if (!fileInput.files[0]) {
        showAlert('warning', 'Por favor selecciona un archivo');
        return;
    }

    const formData = new FormData();
    formData.append('file', fileInput.files[0]);
    formData.append('lottery_type', lotteryType);

    // Show loading state
    const resultDiv = document.getElementById('conversionResult');
    resultDiv.style.display = 'block';
    resultDiv.innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-info" role="status">
                <span class="visually-hidden">Convirtiendo...</span>
            </div>
            <p class="mt-3">Convirtiendo formato...</p>
        </div>
    `;

    // Disable buttons
    document.querySelector('#formatConverterModal .btn-info').disabled = true;

    // Make request
    fetch('/api/convert_format', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resultDiv.innerHTML = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle"></i> ¡Conversión Exitosa!</h6>
                    <p>${data.message}</p>
                    <ul class="mb-3">
                        <li><strong>Filas convertidas:</strong> ${data.total_rows}</li>
                        <li><strong>Formato detectado:</strong> ${data.format_detected}</li>
                        <li><strong>Archivo generado:</strong> ${data.converted_file}</li>
                    </ul>

                    <h6>Vista previa de los datos convertidos:</h6>
                    <div class="table-responsive">
                        <table class="table table-sm table-striped">
                            <thead>
                                <tr>
                                    ${data.columns.map(col => \`<th>\${col}</th>\`).join('')}
                                </tr>
                            </thead>
                            <tbody>
                                ${data.sample_data.slice(0, 5).map(row =>
                                    \`<tr>\${data.columns.map(col => \`<td>\${row[col] || ''}</td>\`).join('')}</tr>\`
                                ).join('')}
                            </tbody>
                        </table>
                    </div>

                    <div class="alert alert-info">
                        <small>
                            <i class="fas fa-info-circle"></i>
                            Ahora puedes usar el archivo convertido en el formulario de importación principal.
                        </small>
                    </div>
                </div>
            `;
        } else {
            resultDiv.innerHTML = \`
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-triangle"></i> Error en la Conversión</h6>
                    <p>\${data.error}</p>
                    \${data.suggestion ? \`<p><strong>Sugerencia:</strong> \${data.suggestion}</p>\` : ''}
                    \${data.custom_error ? \`<details><summary>Detalles del error personalizado:</summary><pre>\${data.custom_error}</pre></details>\` : ''}
                    \${data.general_error ? \`<details><summary>Detalles del error general:</summary><pre>\${data.general_error}</pre></details>\` : ''}
                </div>
            \`;
        }

        // Re-enable button
        document.querySelector('#formatConverterModal .btn-info').disabled = false;
        document.querySelector('#formatConverterModal .btn-info').innerHTML = '<i class="fas fa-magic"></i> Convertir Otro Archivo';
    })
    .catch(error => {
        resultDiv.innerHTML = \`
            <div class="alert alert-danger">
                <h6><i class="fas fa-exclamation-triangle"></i> Error de Conexión</h6>
                <p>Error: \${error.message}</p>
            </div>
        \`;

        document.querySelector('#formatConverterModal .btn-info').disabled = false;
    });
}

function showClearDataOptions() {
    const modal = `
        <div class="modal fade" id="clearDataModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header bg-danger text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-trash-alt"></i> Eliminar Datos del Sistema
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle"></i> ¡ATENCIÓN!</h6>
                            <p>Esta acción eliminará permanentemente los datos seleccionados del sistema.</p>
                            <p class="mb-0"><strong>Esta operación NO se puede deshacer.</strong></p>
                        </div>

                        <div class="mb-3">
                            <label for="clearDataType" class="form-label">¿Qué datos deseas eliminar?</label>
                            <select class="form-select" id="clearDataType">
                                <option value="all">🗑️ Todos los datos (Euromillones + Loto Francia)</option>
                                <option value="euromillones">🌟 Solo datos de Euromillones</option>
                                <option value="loto_france">🍀 Solo datos de Loto Francia</option>
                            </select>
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="createBackupBeforeDelete" checked>
                            <label class="form-check-label" for="createBackupBeforeDelete">
                                <strong>Crear backup antes de eliminar</strong> (Recomendado)
                            </label>
                            <div class="form-text">
                                Se creará una copia de seguridad en archivos CSV antes de la eliminación.
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> ¿Por qué eliminar datos?</h6>
                            <ul class="mb-0">
                                <li>Para cargar un conjunto completamente nuevo de datos</li>
                                <li>Para limpiar datos incorrectos o corruptos</li>
                                <li>Para empezar de cero con datos más actualizados</li>
                                <li>Para liberar espacio en la base de datos</li>
                            </ul>
                        </div>

                        <div id="clearDataCurrentStats" class="mt-3">
                            <h6>📊 Estado actual de los datos:</h6>
                            <div class="text-center">
                                <div class="spinner-border spinner-border-sm" role="status">
                                    <span class="visually-hidden">Cargando...</span>
                                </div>
                                <span class="ms-2">Obteniendo estadísticas...</span>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="button" class="btn btn-danger" onclick="executeClearData()" id="clearDataBtn">
                            <i class="fas fa-trash-alt"></i> Eliminar Datos
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modal);
    const modalElement = new bootstrap.Modal(document.getElementById('clearDataModal'));
    modalElement.show();

    // Load current statistics
    loadCurrentDataStats();

    // Clean up modal after hiding
    document.getElementById('clearDataModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function loadCurrentDataStats() {
    fetch('/api/data_status')
        .then(response => response.json())
        .then(data => {
            const statsDiv = document.getElementById('clearDataCurrentStats');
            statsDiv.innerHTML = \`
                <h6>📊 Estado actual de los datos:</h6>
                <div class="row text-center">
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body py-2">
                                <h5 class="card-title text-primary">\${data.euromillones.count}</h5>
                                <p class="card-text small">🌟 Euromillones</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body py-2">
                                <h5 class="card-title text-success">\${data.loto_france.count}</h5>
                                <p class="card-text small">🍀 Loto Francia</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body py-2">
                                <h5 class="card-title text-warning">\${data.total_draws}</h5>
                                <p class="card-text small">📊 Total</p>
                            </div>
                        </div>
                    </div>
                </div>
            \`;
        })
        .catch(error => {
            document.getElementById('clearDataCurrentStats').innerHTML = \`
                <div class="alert alert-warning">
                    <small>No se pudieron cargar las estadísticas: \${error.message}</small>
                </div>
            \`;
        });
}

function executeClearData() {
    const dataType = document.getElementById('clearDataType').value;
    const createBackup = document.getElementById('createBackupBeforeDelete').checked;

    // Get confirmation tokens
    const confirmTokens = {
        'all': 'DELETE_ALL_DATA',
        'euromillones': 'DELETE_EUROMILLONES',
        'loto_france': 'DELETE_LOTO_FRANCE'
    };

    const requiredToken = confirmTokens[dataType];
    const dataTypeNames = {
        'all': 'TODOS LOS DATOS',
        'euromillones': 'DATOS DE EUROMILLONES',
        'loto_france': 'DATOS DE LOTO FRANCIA'
    };

    // Show confirmation dialog
    const confirmText = prompt(\`
⚠️ CONFIRMACIÓN REQUERIDA ⚠️

Estás a punto de eliminar: \${dataTypeNames[dataType]}

Esta acción NO se puede deshacer.

Para confirmar, escribe exactamente: \${requiredToken}
    \`);

    if (confirmText !== requiredToken) {
        showAlert('warning', 'Eliminación cancelada. Token de confirmación incorrecto.');
        return;
    }

    // Show loading state
    const modalBody = document.querySelector('#clearDataModal .modal-body');
    modalBody.innerHTML = \`
        <div class="text-center">
            <div class="spinner-border text-danger" role="status">
                <span class="visually-hidden">Eliminando...</span>
            </div>
            <p class="mt-3">\${createBackup ? 'Creando backup y eliminando datos...' : 'Eliminando datos...'}</p>
            <p class="text-muted">Esta operación puede tomar varios minutos.</p>
        </div>
    \`;

    // Disable buttons
    document.querySelector('#clearDataModal .btn-close').disabled = true;
    document.querySelector('#clearDataModal .modal-footer').style.display = 'none';

    // Make request
    fetch('/api/clear_data', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            lottery_type: dataType,
            create_backup: createBackup,
            confirm_token: requiredToken
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            modalBody.innerHTML = \`
                <div class="text-center">
                    <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                    <h5 class="text-success">¡Datos Eliminados Exitosamente!</h5>
                    <p>\${data.message}</p>

                    <div class="row mt-3">
                        <div class="col-md-6">
                            <h6>📊 Antes:</h6>
                            <ul class="list-unstyled">
                                <li>🌟 Euromillones: \${data.stats_before.euromillones.count}</li>
                                <li>🍀 Loto Francia: \${data.stats_before.loto_france.count}</li>
                                <li>📊 Total: \${data.stats_before.total}</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>📊 Después:</h6>
                            <ul class="list-unstyled">
                                <li>🌟 Euromillones: \${data.stats_after.euromillones.count}</li>
                                <li>🍀 Loto Francia: \${data.stats_after.loto_france.count}</li>
                                <li>📊 Total: \${data.stats_after.total}</li>
                            </ul>
                        </div>
                    </div>

                    \${data.backup_info ? \`
                        <div class="alert alert-info mt-3">
                            <h6><i class="fas fa-download"></i> Backup Creado</h6>
                            <p>Se creó un backup en: <code>\${data.backup_info.backup_dir}</code></p>
                            <small>Archivos: \${data.backup_info.backup_files.join(', ')}</small>
                        </div>
                    \` : ''}

                    <div class="alert alert-success mt-3">
                        <small>
                            <i class="fas fa-info-circle"></i>
                            Ahora puedes importar nuevos datos usando el formulario principal.
                        </small>
                    </div>
                </div>
            \`;
        } else {
            modalBody.innerHTML = \`
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle text-danger fa-3x mb-3"></i>
                    <h5 class="text-danger">Error al Eliminar Datos</h5>
                    <p>\${data.error}</p>
                    \${data.suggestion ? \`<p><strong>Sugerencia:</strong> \${data.suggestion}</p>\` : ''}
                </div>
            \`;
        }

        // Show close button
        document.querySelector('#clearDataModal .modal-footer').innerHTML = \`
            <button type="button" class="btn btn-primary" data-bs-dismiss="modal" onclick="checkCurrentData()">
                <i class="fas fa-check"></i> Continuar
            </button>
        \`;
        document.querySelector('#clearDataModal .modal-footer').style.display = 'block';
    })
    .catch(error => {
        modalBody.innerHTML = \`
            <div class="text-center">
                <i class="fas fa-exclamation-triangle text-danger fa-3x mb-3"></i>
                <h5 class="text-danger">Error de Conexión</h5>
                <p>Error: \${error.message}</p>
            </div>
        \`;

        document.querySelector('#clearDataModal .modal-footer').innerHTML = \`
            <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Cerrar</button>
        \`;
        document.querySelector('#clearDataModal .modal-footer').style.display = 'block';
    });
}

function createBackup() {
    showAlert('info', 'Creando backup...');

    fetch('/api/backup_data', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', \`Backup creado exitosamente: \${data.backup_dir}\`);
        } else {
            showAlert('danger', \`Error creando backup: \${data.error}\`);
        }
    })
    .catch(error => {
        showAlert('danger', \`Error de conexión: \${error.message}\`);
    });
}
</script>
{% endblock %}
