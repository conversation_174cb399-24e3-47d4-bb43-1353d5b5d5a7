{% extends "base.html" %}

{% block title %}Importar Datos - Sistema de Análisis de Loterías{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1>
            <i class="fas fa-upload"></i> Importar Datos Históricos
        </h1>
        <p class="text-muted">
            Importa datos históricos de sorteos desde archivos CSV, TXT o XLSX.
        </p>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-file-upload"></i> Subir Archivo
                </h5>
            </div>
            <div class="card-body">
                <form action="{{ url_for('upload_data') }}" method="post" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="lottery_type" class="form-label">Tipo de Lotería</label>
                        <select class="form-select" id="lottery_type" name="lottery_type" required>
                            <option value="">Selecciona una lotería</option>
                            <option value="euromillones">Euromillones</option>
                            <option value="loto_france">Loto Francia</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="file" class="form-label">Archivo de Datos</label>
                        <input type="file" class="form-control" id="file" name="file" 
                               accept=".csv,.txt,.xlsx" required>
                        <div class="form-text">
                            Formatos soportados: CSV, TXT, XLSX (máximo 16MB)
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload"></i> Importar Datos
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle"></i> Formato de Datos
                </h5>
            </div>
            <div class="card-body">
                <h6>Euromillones</h6>
                <p class="small">
                    Debe contener: fecha, 5 números principales (1-50), 2 estrellas (1-12)
                </p>
                
                <h6>Loto Francia</h6>
                <p class="small">
                    Debe contener: fecha, 5 números principales (1-49), 1 número Chance (1-10)
                </p>
                
                <h6>Ejemplo CSV:</h6>
                <pre class="small bg-light p-2">
fecha,num1,num2,num3,num4,num5,star1,star2
2024-01-01,5,12,23,34,45,3,8
2024-01-05,8,15,27,38,42,1,11
                </pre>
                
                <div class="alert alert-warning small">
                    <i class="fas fa-exclamation-triangle"></i>
                    El sistema validará automáticamente los datos y reportará errores.
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-download"></i> Datos de Ejemplo
                </h5>
            </div>
            <div class="card-body">
                <p class="small">Descarga archivos de ejemplo para ver el formato correcto:</p>
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary btn-sm" onclick="downloadExample('euromillones')">
                        <i class="fas fa-download"></i> Ejemplo Euromillones
                    </button>
                    <button class="btn btn-outline-info btn-sm" onclick="downloadExample('loto_france')">
                        <i class="fas fa-download"></i> Ejemplo Loto Francia
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list"></i> Instrucciones Detalladas
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Preparación del Archivo</h6>
                        <ol>
                            <li>Asegúrate de que el archivo tenga encabezados de columna</li>
                            <li>Las fechas deben estar en formato DD/MM/YYYY o YYYY-MM-DD</li>
                            <li>Los números deben ser enteros válidos</li>
                            <li>No debe haber filas vacías o datos faltantes</li>
                        </ol>
                    </div>
                    <div class="col-md-6">
                        <h6>Validaciones Automáticas</h6>
                        <ul>
                            <li>Formato de fecha válido</li>
                            <li>Números dentro del rango permitido</li>
                            <li>Cantidad correcta de números por sorteo</li>
                            <li>Detección de duplicados</li>
                        </ul>
                    </div>
                </div>
                
                <div class="alert alert-info mt-3">
                    <h6><i class="fas fa-lightbulb"></i> Consejos</h6>
                    <ul class="mb-0">
                        <li>Si tienes datos en formato diferente, puedes usar Excel para reorganizar las columnas</li>
                        <li>El sistema detectará automáticamente separadores en archivos TXT</li>
                        <li>Los sorteos duplicados serán omitidos automáticamente</li>
                        <li>Recibirás un reporte detallado de la importación</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function downloadExample(lotteryType) {
    let csvContent = '';
    
    if (lotteryType === 'euromillones') {
        csvContent = `fecha,num1,num2,num3,num4,num5,star1,star2,jackpot,winners
2024-01-01,5,12,23,34,45,3,8,50000000,1
2024-01-05,8,15,27,38,42,1,11,60000000,0
2024-01-09,2,19,31,41,47,4,9,70000000,2
2024-01-12,7,14,25,36,49,2,10,80000000,1
2024-01-16,11,18,29,40,44,5,7,90000000,0`;
    } else {
        csvContent = `fecha,num1,num2,num3,num4,num5,chance,jackpot,winners
2024-01-01,5,12,23,34,45,3,5000000,1
2024-01-03,8,15,27,38,42,7,6000000,0
2024-01-06,2,19,31,41,47,2,7000000,2
2024-01-08,7,14,25,36,49,9,8000000,1
2024-01-10,11,18,29,40,44,5,9000000,0`;
    }
    
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `ejemplo_${lotteryType}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}
</script>
{% endblock %}
