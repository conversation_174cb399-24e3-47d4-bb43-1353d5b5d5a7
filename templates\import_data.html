{% extends "base.html" %}

{% block title %}Importar Datos Históricos{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">
                    <i class="fas fa-upload text-primary"></i>
                    Importar Datos Históricos
                </h1>
                <div class="text-muted">
                    <small>Importa datos históricos de sorteos desde archivos CSV, TXT o XLSX</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Estado Actual de los Datos -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info">
                <h6 class="alert-heading">
                    <i class="fas fa-info-circle"></i> Estado Actual de los Datos
                </h6>
                <p class="mb-2">
                    El sistema contiene actualmente: <span id="currentDataCount"><strong>0</strong> sorteos</span>
                </p>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="checkCurrentData()">
                        <i class="fas fa-sync-alt"></i> Actualizar Estado
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-success" onclick="scrapeRealData()">
                        <i class="fas fa-globe"></i> Obtener Datos Reales
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-warning" onclick="cleanDuplicates()">
                        <i class="fas fa-broom"></i> Limpiar Duplicados
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="showClearDataOptions()">
                        <i class="fas fa-trash-alt"></i> Eliminar Datos
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Formulario de Importación -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-file-upload"></i> Subir Archivo
                    </h5>
                </div>
                <div class="card-body">
                    <form id="importForm" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="lottery_type" class="form-label">Tipo de Lotería</label>
                            <select class="form-select" id="lottery_type" name="lottery_type" required>
                                <option value="">Selecciona una lotería</option>
                                <option value="euromillones">🌟 Euromillones</option>
                                <option value="loto_france">🍀 Loto Francia</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="file" class="form-label">Archivo de Datos</label>
                            <input type="file" class="form-control" id="file" name="file"
                                   accept=".csv,.txt,.xlsx" required>
                            <div class="form-text">
                                Formatos soportados: CSV, TXT, XLSX. Tamaño máximo: 10MB
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="skip_duplicates"
                                       name="skip_duplicates" checked>
                                <label class="form-check-label" for="skip_duplicates">
                                    Omitir duplicados automáticamente
                                </label>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload"></i> Importar Datos
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Formato de Datos -->
            <div class="card mb-3">
                <div class="card-header bg-info text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-table"></i> Formato de Datos
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6>Euromillones</h6>
                        <small class="text-muted">
                            Debe contener 5 números principales (1-50) y 2 estrellas (1-12). Puedes incluir fecha, jackpot y ganadores.
                        </small>
                    </div>
                    <div class="mb-3">
                        <h6>Loto Francia</h6>
                        <small class="text-muted">
                            Debe contener 5 números principales (1-49) y 1 número chance (1-10). Puedes incluir fecha, jackpot y ganadores.
                        </small>
                    </div>
                    <div class="mb-3">
                        <h6>Ejemplo CSV</h6>
                        <code class="small">
                            fecha,num1,num2,num3,num4,num5,star1,star2<br>
                            2024-01-01,5,12,23,34,45,3,8
                        </code>
                    </div>
                </div>
            </div>

            <!-- Datos de Ejemplo -->
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-download"></i> Datos de Ejemplo
                    </h6>
                </div>
                <div class="card-body">
                    <p class="small text-muted">
                        Descarga archivos de ejemplo para ver el formato correcto:
                    </p>
                    <div class="d-grid gap-2">
                        <button class="btn btn-sm btn-outline-primary" onclick="downloadExample('euromillones')">
                            <i class="fas fa-download"></i> Ejemplo Euromillones
                        </button>
                        <button class="btn btn-sm btn-outline-success" onclick="downloadExample('loto_france')">
                            <i class="fas fa-download"></i> Ejemplo Loto Francia
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Herramientas Avanzadas -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-tools"></i> Herramientas Avanzadas
                    </h6>
                </div>
                <div class="card-body">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-secondary" onclick="createBackup()">
                            <i class="fas fa-save"></i> Crear Backup
                        </button>
                        <button type="button" class="btn btn-outline-info" onclick="showDataSources()">
                            <i class="fas fa-link"></i> Fuentes Oficiales
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Resultado de la importación -->
<div id="importResult" class="mt-4" style="display: none;"></div>
{% endblock %}

{% block extra_js %}
<script>
console.log('🚀 Cargando JavaScript para importación de datos...');

// ===== FUNCIONES BÁSICAS =====
function showAlert(type, message) {
    console.log('📢 Alerta:', type, message);
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-' + type + ' alert-dismissible fade show';
    alertDiv.innerHTML = message + '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';

    const container = document.querySelector('.container');
    container.insertBefore(alertDiv, container.firstChild);

    setTimeout(function() {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// ===== FUNCIONES PRINCIPALES =====
function checkCurrentData() {
    console.log('✅ checkCurrentData ejecutada');
    showAlert('info', 'Verificando estado de los datos...');

    fetch('/api/data_status')
        .then(function(response) { return response.json(); })
        .then(function(data) {
            const total = data.total_draws;
            const euroCount = data.euromillones.count;
            const lotoCount = data.loto_france.count;

            document.getElementById('currentDataCount').innerHTML =
                '<strong>' + total + '</strong> (' + euroCount + ' Euromillones + ' + lotoCount + ' Loto Francia)';

            showAlert('success', 'Estado actualizado: ' + total + ' sorteos en total');
        })
        .catch(function(error) {
            showAlert('danger', 'Error al verificar el estado: ' + error.message);
        });
}

function scrapeRealData() {
    console.log('✅ scrapeRealData ejecutada');
    showAlert('info', 'Función de scraping en desarrollo. Usa importación manual por ahora.');
}

function cleanDuplicates() {
    console.log('✅ cleanDuplicates ejecutada');
    showAlert('info', 'Función de limpieza de duplicados en desarrollo.');
}

function showClearDataOptions() {
    console.log('✅ showClearDataOptions ejecutada');
    showAlert('warning', 'Función de eliminación de datos en desarrollo.');
}

function createBackup() {
    console.log('✅ createBackup ejecutada');
    showAlert('info', 'Creando backup...');

    fetch('/api/backup_data', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
    })
    .then(function(response) { return response.json(); })
    .then(function(data) {
        if (data.success) {
            showAlert('success', 'Backup creado exitosamente');
        } else {
            showAlert('danger', 'Error creando backup: ' + data.error);
        }
    })
    .catch(function(error) {
        showAlert('danger', 'Error de conexión: ' + error.message);
    });
}

function showDataSources() {
    console.log('✅ showDataSources ejecutada');
    alert('Fuentes Oficiales:\n\n🌟 EUROMILLONES:\n• Loterías y Apuestas del Estado\n• Euro-millions.com\n\n🍀 LOTO FRANCIA:\n• FDJ - Française des Jeux\n• Loto.fr');
}

function downloadExample(lotteryType) {
    console.log('✅ downloadExample ejecutada para:', lotteryType);
    let csvContent = '';
    let filename = '';

    if (lotteryType === 'euromillones') {
        csvContent = 'date,num1,num2,num3,num4,num5,star1,star2,jackpot,winners\n';
        csvContent += '2024-01-01,5,12,23,34,45,3,8,15000000,0\n';
        csvContent += '2024-01-05,8,15,27,38,42,1,11,20000000,1\n';
        filename = 'ejemplo_euromillones.csv';
    } else {
        csvContent = 'date,num1,num2,num3,num4,num5,chance,jackpot,winners\n';
        csvContent += '2024-01-01,7,14,21,28,35,6,2000000,0\n';
        csvContent += '2024-01-04,3,16,22,33,41,2,3000000,1\n';
        filename = 'ejemplo_loto_francia.csv';
    }

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showAlert('success', 'Archivo de ejemplo descargado: ' + filename);
}

// ===== INICIALIZACIÓN =====
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Página cargada - Inicializando...');

    // Verificar estado inicial
    setTimeout(function() {
        checkCurrentData();
    }, 1000);

    // Test de funciones
    console.log('✅ Funciones disponibles:', {
        checkCurrentData: typeof checkCurrentData,
        scrapeRealData: typeof scrapeRealData,
        cleanDuplicates: typeof cleanDuplicates,
        showClearDataOptions: typeof showClearDataOptions,
        createBackup: typeof createBackup,
        showDataSources: typeof showDataSources,
        downloadExample: typeof downloadExample
    });

    console.log('✅ JavaScript cargado completamente');
});
</script>
{% endblock %}