{% extends "base.html" %}

{% block title %}Importar Datos Históricos{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3">
                    <i class="fas fa-upload text-primary"></i>
                    Importar Datos Históricos
                </h1>
                <div class="text-muted">
                    <small>Importa datos históricos de sorteos desde archivos CSV, TXT o XLSX</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Estado Actual de los Datos -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info">
                <h6 class="alert-heading">
                    <i class="fas fa-info-circle"></i> Estado Actual de los Datos
                </h6>
                <p class="mb-2">
                    El sistema contiene actualmente: <span id="currentDataCount"><strong>0</strong> sorteos</span>
                </p>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="checkCurrentData()">
                        <i class="fas fa-sync-alt"></i> Actualizar Estado
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-success" onclick="scrapeRealData()">
                        <i class="fas fa-globe"></i> Obtener Datos Reales
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-warning" onclick="cleanDuplicates()">
                        <i class="fas fa-broom"></i> Limpiar Duplicados
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="showClearDataOptions()">
                        <i class="fas fa-trash-alt"></i> Eliminar Datos
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-info" onclick="showFormatConverter()">
                        <i class="fas fa-magic"></i> Convertidor de Formatos
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Formulario de Importación -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-file-upload"></i> Subir Archivo
                    </h5>
                </div>
                <div class="card-body">
                    <form id="importForm" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="lottery_type" class="form-label">Tipo de Lotería</label>
                            <select class="form-select" id="lottery_type" name="lottery_type" required>
                                <option value="">Selecciona una lotería</option>
                                <option value="euromillones">🌟 Euromillones</option>
                                <option value="loto_france">🍀 Loto Francia</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="file" class="form-label">Archivo de Datos</label>
                            <input type="file" class="form-control" id="file" name="file"
                                   accept=".csv,.txt,.xlsx" required>
                            <div class="form-text">
                                Formatos soportados: CSV, TXT, XLSX. Tamaño máximo: 10MB
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="skip_duplicates"
                                       name="skip_duplicates" checked>
                                <label class="form-check-label" for="skip_duplicates">
                                    Omitir duplicados automáticamente
                                </label>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload"></i> Importar Datos
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Formato de Datos -->
            <div class="card mb-3">
                <div class="card-header bg-info text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-table"></i> Formato de Datos
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6>Euromillones</h6>
                        <small class="text-muted">
                            Debe contener 5 números principales (1-50) y 2 estrellas (1-12). Puedes incluir fecha, jackpot y ganadores.
                        </small>
                    </div>
                    <div class="mb-3">
                        <h6>Loto Francia</h6>
                        <small class="text-muted">
                            Debe contener 5 números principales (1-49) y 1 número chance (1-10). Puedes incluir fecha, jackpot y ganadores.
                        </small>
                    </div>
                    <div class="mb-3">
                        <h6>Ejemplo CSV</h6>
                        <code class="small">
                            fecha,num1,num2,num3,num4,num5,star1,star2<br>
                            2024-01-01,5,12,23,34,45,3,8
                        </code>
                    </div>
                </div>
            </div>

            <!-- Datos de Ejemplo -->
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-download"></i> Datos de Ejemplo
                    </h6>
                </div>
                <div class="card-body">
                    <p class="small text-muted">
                        Descarga archivos de ejemplo para ver el formato correcto:
                    </p>
                    <div class="d-grid gap-2">
                        <button class="btn btn-sm btn-outline-primary" onclick="downloadExample('euromillones')">
                            <i class="fas fa-download"></i> Ejemplo Euromillones
                        </button>
                        <button class="btn btn-sm btn-outline-success" onclick="downloadExample('loto_france')">
                            <i class="fas fa-download"></i> Ejemplo Loto Francia
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Herramientas Avanzadas -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-tools"></i> Herramientas Avanzadas
                    </h6>
                </div>
                <div class="card-body">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-secondary" onclick="createBackup()">
                            <i class="fas fa-save"></i> Crear Backup
                        </button>
                        <button type="button" class="btn btn-outline-info" onclick="showDataSources()">
                            <i class="fas fa-link"></i> Fuentes Oficiales
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Resultado de la importación -->
<div id="importResult" class="mt-4" style="display: none;"></div>
{% endblock %}

{% block extra_js %}
<script>
console.log('🚀 Cargando JavaScript para importación de datos...');

// ===== FUNCIONES BÁSICAS =====
function showAlert(type, message) {
    console.log('📢 Alerta:', type, message);
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-' + type + ' alert-dismissible fade show';
    alertDiv.innerHTML = message + '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';

    const container = document.querySelector('.container');
    container.insertBefore(alertDiv, container.firstChild);

    setTimeout(function() {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// ===== FUNCIONES PRINCIPALES =====
function checkCurrentData() {
    console.log('✅ checkCurrentData ejecutada');
    showAlert('info', 'Verificando estado de los datos...');

    fetch('/api/data_status')
        .then(function(response) { return response.json(); })
        .then(function(data) {
            const total = data.total_draws;
            const euroCount = data.euromillones.count;
            const lotoCount = data.loto_france.count;

            document.getElementById('currentDataCount').innerHTML =
                '<strong>' + total + '</strong> (' + euroCount + ' Euromillones + ' + lotoCount + ' Loto Francia)';

            showAlert('success', 'Estado actualizado: ' + total + ' sorteos en total');
        })
        .catch(function(error) {
            showAlert('danger', 'Error al verificar el estado: ' + error.message);
        });
}

function scrapeRealData() {
    console.log('✅ scrapeRealData ejecutada');

    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = 'scrapeModal';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-globe"></i> Obtener Datos Reales
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Tipo de Lotería:</label>
                        <select class="form-select" id="scrapeType">
                            <option value="both">Ambas loterías</option>
                            <option value="euromillones">Solo Euromillones</option>
                            <option value="loto_france">Solo Loto Francia</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Máximo de resultados:</label>
                        <select class="form-select" id="maxResults">
                            <option value="25">25 sorteos</option>
                            <option value="50" selected>50 sorteos</option>
                            <option value="100">100 sorteos</option>
                        </select>
                    </div>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Nota:</strong> El scraping puede fallar si los sitios web han cambiado.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-success" onclick="executeScraping()">
                        <i class="fas fa-download"></i> Iniciar Scraping
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();

    modal.addEventListener('hidden.bs.modal', function() {
        modal.remove();
    });
}

function executeScraping() {
    const lotteryType = document.getElementById('scrapeType').value;
    const maxResults = parseInt(document.getElementById('maxResults').value);
    const modal = document.getElementById('scrapeModal');
    const modalBody = modal.querySelector('.modal-body');

    modalBody.innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-success" role="status"></div>
            <p class="mt-3">Obteniendo datos reales desde fuentes oficiales...</p>
            <p class="text-muted">Esto puede tomar varios minutos.</p>
        </div>
    `;

    fetch('/api/scrape_real_data', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            lottery_type: lotteryType,
            max_results: maxResults
        })
    })
    .then(function(response) { return response.json(); })
    .then(function(data) {
        if (data.success) {
            modalBody.innerHTML = `
                <div class="text-center">
                    <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                    <h5 class="text-success">¡Datos Obtenidos Exitosamente!</h5>
                    <p>Se obtuvieron <strong>${data.results.total_count}</strong> sorteos reales:</p>
                    <ul class="list-unstyled">
                        <li>📊 Euromillones: ${data.results.euromillones_count} sorteos</li>
                        <li>📊 Loto Francia: ${data.results.loto_france_count} sorteos</li>
                    </ul>
                </div>
            `;
            showAlert('success', 'Datos reales obtenidos exitosamente');
            setTimeout(function() { checkCurrentData(); }, 2000);
        } else {
            modalBody.innerHTML = `
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle text-warning fa-3x mb-3"></i>
                    <h5 class="text-warning">No se Pudieron Obtener Datos</h5>
                    <p>${data.message}</p>
                    <div class="alert alert-info">
                        <small>Intenta descargar datos manualmente desde fuentes oficiales.</small>
                    </div>
                </div>
            `;
            showAlert('warning', 'No se pudieron obtener datos reales');
        }

        modal.querySelector('.modal-footer').innerHTML = `
            <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Cerrar</button>
        `;
    })
    .catch(function(error) {
        modalBody.innerHTML = `
            <div class="text-center">
                <i class="fas fa-exclamation-triangle text-danger fa-3x mb-3"></i>
                <h5 class="text-danger">Error de Conexión</h5>
                <p>Error: ${error.message}</p>
            </div>
        `;
        showAlert('danger', 'Error de conexión: ' + error.message);
    });
}

function cleanDuplicates() {
    console.log('✅ cleanDuplicates ejecutada');

    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = 'cleanModal';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title">
                        <i class="fas fa-broom"></i> Limpiar Duplicados
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Tipo de Lotería:</label>
                        <select class="form-select" id="cleanType">
                            <option value="both">Ambas loterías</option>
                            <option value="euromillones">Solo Euromillones</option>
                            <option value="loto_france">Solo Loto Francia</option>
                        </select>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="dryRun" checked>
                        <label class="form-check-label" for="dryRun">
                            <strong>Modo de prueba</strong> (solo mostrar qué se eliminaría)
                        </label>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        Se eliminan sorteos duplicados basándose en la fecha. Se mantiene el más reciente.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-warning" onclick="executeCleanDuplicates()">
                        <i class="fas fa-broom"></i> Limpiar Duplicados
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();

    modal.addEventListener('hidden.bs.modal', function() {
        modal.remove();
    });
}

function executeCleanDuplicates() {
    const lotteryType = document.getElementById('cleanType').value;
    const dryRun = document.getElementById('dryRun').checked;
    const modal = document.getElementById('cleanModal');
    const modalBody = modal.querySelector('.modal-body');

    modalBody.innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-warning" role="status"></div>
            <p class="mt-3">${dryRun ? 'Analizando duplicados...' : 'Eliminando duplicados...'}</p>
        </div>
    `;

    fetch('/api/clean_duplicates', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            lottery_type: lotteryType,
            dry_run: dryRun
        })
    })
    .then(function(response) { return response.json(); })
    .then(function(data) {
        if (data.success) {
            modalBody.innerHTML = `
                <div class="text-center">
                    <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                    <h5 class="text-success">${dryRun ? 'Análisis Completado' : 'Limpieza Completada'}</h5>
                    <p>${data.message}</p>
                    ${data.removed_count > 0 ? `
                        <div class="alert alert-${dryRun ? 'info' : 'success'}">
                            <strong>${data.removed_count}</strong> duplicados ${dryRun ? 'encontrados' : 'eliminados'}
                        </div>
                    ` : `
                        <div class="alert alert-success">
                            <i class="fas fa-thumbs-up"></i> No se encontraron duplicados
                        </div>
                    `}
                </div>
            `;
            showAlert('success', data.message);
            if (!dryRun && data.removed_count > 0) {
                setTimeout(function() { checkCurrentData(); }, 2000);
            }
        } else {
            modalBody.innerHTML = `
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle text-danger fa-3x mb-3"></i>
                    <h5 class="text-danger">Error</h5>
                    <p>Error: ${data.error}</p>
                </div>
            `;
            showAlert('danger', 'Error: ' + data.error);
        }

        modal.querySelector('.modal-footer').innerHTML = `
            <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Cerrar</button>
        `;
    })
    .catch(function(error) {
        modalBody.innerHTML = `
            <div class="text-center">
                <i class="fas fa-exclamation-triangle text-danger fa-3x mb-3"></i>
                <h5 class="text-danger">Error de Conexión</h5>
                <p>Error: ${error.message}</p>
            </div>
        `;
        showAlert('danger', 'Error de conexión: ' + error.message);
    });
}

function showClearDataOptions() {
    console.log('✅ showClearDataOptions ejecutada');

    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = 'clearModal';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-trash-alt"></i> Eliminar Datos
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Tipo de Datos a Eliminar:</label>
                        <select class="form-select" id="clearDataType">
                            <option value="all">Todos los datos</option>
                            <option value="euromillones">Solo Euromillones</option>
                            <option value="loto_france">Solo Loto Francia</option>
                        </select>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="createBackup" checked>
                        <label class="form-check-label" for="createBackup">
                            <strong>Crear backup antes de eliminar</strong> (recomendado)
                        </label>
                    </div>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>¡PELIGRO!</strong> Esta acción NO se puede deshacer.
                        Se te pedirá un token de confirmación.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-danger" onclick="executeClearData()">
                        <i class="fas fa-trash-alt"></i> Eliminar Datos
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();

    modal.addEventListener('hidden.bs.modal', function() {
        modal.remove();
    });
}

function executeClearData() {
    const dataType = document.getElementById('clearDataType').value;
    const createBackup = document.getElementById('createBackup').checked;

    const confirmTokens = {
        'all': 'DELETE_ALL_DATA',
        'euromillones': 'DELETE_EUROMILLONES',
        'loto_france': 'DELETE_LOTO_FRANCE'
    };

    const dataTypeNames = {
        'all': 'TODOS LOS DATOS',
        'euromillones': 'DATOS DE EUROMILLONES',
        'loto_france': 'DATOS DE LOTO FRANCIA'
    };

    const requiredToken = confirmTokens[dataType];
    const confirmText = prompt(`
⚠️ CONFIRMACIÓN REQUERIDA ⚠️

Estás a punto de eliminar: ${dataTypeNames[dataType]}

Esta acción NO se puede deshacer.

Para confirmar, escribe exactamente: ${requiredToken}
    `);

    if (confirmText !== requiredToken) {
        showAlert('warning', 'Eliminación cancelada. Token de confirmación incorrecto.');
        return;
    }

    const modal = document.getElementById('clearModal');
    const modalBody = modal.querySelector('.modal-body');

    modalBody.innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-danger" role="status"></div>
            <p class="mt-3">${createBackup ? 'Creando backup y eliminando datos...' : 'Eliminando datos...'}</p>
            <p class="text-muted">Esta operación puede tomar varios minutos.</p>
        </div>
    `;

    fetch('/api/clear_data', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            lottery_type: dataType,
            create_backup: createBackup,
            confirm_token: requiredToken
        })
    })
    .then(function(response) { return response.json(); })
    .then(function(data) {
        if (data.success) {
            let backupHtml = '';
            if (data.backup_info) {
                backupHtml = `
                    <div class="alert alert-info mt-3">
                        <h6><i class="fas fa-download"></i> Backup Creado</h6>
                        <p>Backup en: <code>${data.backup_info.backup_dir}</code></p>
                    </div>
                `;
            }

            modalBody.innerHTML = `
                <div class="text-center">
                    <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                    <h5 class="text-success">¡Datos Eliminados Exitosamente!</h5>
                    <p>${data.message}</p>
                    ${backupHtml}
                    <div class="alert alert-success mt-3">
                        <small>Ahora puedes importar nuevos datos.</small>
                    </div>
                </div>
            `;
            showAlert('success', 'Datos eliminados exitosamente');
            setTimeout(function() { checkCurrentData(); }, 2000);
        } else {
            modalBody.innerHTML = `
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle text-danger fa-3x mb-3"></i>
                    <h5 class="text-danger">Error al Eliminar Datos</h5>
                    <p>${data.error}</p>
                </div>
            `;
            showAlert('danger', 'Error eliminando datos: ' + data.error);
        }

        modal.querySelector('.modal-footer').innerHTML = `
            <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Continuar</button>
        `;
    })
    .catch(function(error) {
        modalBody.innerHTML = `
            <div class="text-center">
                <i class="fas fa-exclamation-triangle text-danger fa-3x mb-3"></i>
                <h5 class="text-danger">Error de Conexión</h5>
                <p>Error: ${error.message}</p>
            </div>
        `;
        showAlert('danger', 'Error de conexión: ' + error.message);

        modal.querySelector('.modal-footer').innerHTML = `
            <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Cerrar</button>
        `;
    });
}

function createBackup() {
    console.log('✅ createBackup ejecutada');
    showAlert('info', 'Creando backup...');

    fetch('/api/backup_data', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
    })
    .then(function(response) { return response.json(); })
    .then(function(data) {
        if (data.success) {
            showAlert('success', 'Backup creado exitosamente');
        } else {
            showAlert('danger', 'Error creando backup: ' + data.error);
        }
    })
    .catch(function(error) {
        showAlert('danger', 'Error de conexión: ' + error.message);
    });
}

function showDataSources() {
    console.log('✅ showDataSources ejecutada');

    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = 'sourcesModal';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-info text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-link"></i> Fuentes Oficiales de Datos
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">🌟 Euromillones</h6>
                                </div>
                                <div class="card-body">
                                    <div class="list-group list-group-flush">
                                        <a href="https://www.loteriasyapuestas.es/es/euromillones/resultados" target="_blank" class="list-group-item list-group-item-action">
                                            <strong>Loterías y Apuestas del Estado</strong><br>
                                            <small class="text-muted">España - Oficial</small>
                                        </a>
                                        <a href="https://www.euro-millions.com/results" target="_blank" class="list-group-item list-group-item-action">
                                            <strong>Euro-millions.com</strong><br>
                                            <small class="text-muted">Internacional</small>
                                        </a>
                                        <a href="https://www.fdj.fr/jeux/jeux-de-tirage/euromillions" target="_blank" class="list-group-item list-group-item-action">
                                            <strong>FDJ Francia</strong><br>
                                            <small class="text-muted">Francia - Oficial</small>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">🍀 Loto Francia</h6>
                                </div>
                                <div class="card-body">
                                    <div class="list-group list-group-flush">
                                        <a href="https://www.fdj.fr/jeux/jeux-de-tirage/loto/resultats-loto" target="_blank" class="list-group-item list-group-item-action">
                                            <strong>FDJ - Française des Jeux</strong><br>
                                            <small class="text-muted">Francia - Oficial</small>
                                        </a>
                                        <a href="https://www.loto.fr/resultats" target="_blank" class="list-group-item list-group-item-action">
                                            <strong>Loto.fr</strong><br>
                                            <small class="text-muted">Francia</small>
                                        </a>
                                        <a href="https://www.tirage-gagnant.com/loto/" target="_blank" class="list-group-item list-group-item-action">
                                            <strong>Tirage Gagnant</strong><br>
                                            <small class="text-muted">Francia</small>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info mt-3">
                        <h6><i class="fas fa-info-circle"></i> Cómo Usar</h6>
                        <ol class="mb-0">
                            <li>Visita las fuentes oficiales</li>
                            <li>Descarga o copia los datos históricos</li>
                            <li>Organiza en formato CSV según los ejemplos</li>
                            <li>Importa usando el formulario de esta página</li>
                        </ol>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();

    modal.addEventListener('hidden.bs.modal', function() {
        modal.remove();
    });
}

function showFormatConverter() {
    console.log('✅ showFormatConverter ejecutada');

    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = 'formatConverterModal';
    modal.innerHTML = `
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header bg-info text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-magic"></i> Convertidor de Formatos de Datos
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0"><i class="fas fa-upload"></i> Archivo a Convertir</h6>
                                </div>
                                <div class="card-body">
                                    <form id="formatConverterForm" enctype="multipart/form-data">
                                        <div class="mb-3">
                                            <label for="converterLotteryType" class="form-label">Tipo de Lotería</label>
                                            <select class="form-select" id="converterLotteryType" required>
                                                <option value="">Selecciona una lotería</option>
                                                <option value="euromillones">🌟 Euromillones</option>
                                                <option value="loto_france">🍀 Loto Francia</option>
                                            </select>
                                        </div>

                                        <div class="mb-3">
                                            <label for="converterFile" class="form-label">Archivo de Datos</label>
                                            <input type="file" class="form-control" id="converterFile"
                                                   accept=".csv,.txt,.xlsx" required>
                                            <div class="form-text">
                                                Formatos: CSV, TXT, XLSX
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">Formato de Entrada</label>
                                            <select class="form-select" id="inputFormat">
                                                <option value="auto">🔍 Detectar Automáticamente</option>
                                                <option value="custom_format">📋 Tu Formato: (30/05/2025,04,07,14,33,36,,01,05)</option>
                                                <option value="standard_format">📊 Formato Estándar con Headers</option>
                                                <option value="semicolon_format">📄 Separado por Punto y Coma</option>
                                                <option value="tab_format">📑 Separado por Tabulaciones</option>
                                            </select>
                                        </div>

                                        <button type="button" class="btn btn-primary" onclick="previewConversion()">
                                            <i class="fas fa-eye"></i> Vista Previa
                                        </button>
                                        <button type="button" class="btn btn-success" onclick="executeConversion()" disabled id="convertBtn">
                                            <i class="fas fa-magic"></i> Convertir
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0"><i class="fas fa-table"></i> Vista Previa</h6>
                                </div>
                                <div class="card-body" id="previewArea">
                                    <div class="text-center text-muted">
                                        <i class="fas fa-file-alt fa-3x mb-3"></i>
                                        <p>Selecciona un archivo y haz clic en "Vista Previa" para ver cómo se convertirán los datos</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> Formatos Soportados</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>Tu Formato Específico:</strong><br>
                                        <code>30/05/2025,04,07,14,33,36,,01,05</code><br>
                                        <small class="text-muted">Fecha DD/MM/YYYY, 5 números principales, campo vacío, 2 estrellas</small>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>Formato Estándar de Salida:</strong><br>
                                        <code>date,num1,num2,num3,num4,num5,star1,star2</code><br>
                                        <small class="text-muted">Fecha YYYY-MM-DD, números ordenados</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
                    <button type="button" class="btn btn-info" onclick="downloadConvertedFile()" disabled id="downloadBtn">
                        <i class="fas fa-download"></i> Descargar Convertido
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();

    modal.addEventListener('hidden.bs.modal', function() {
        modal.remove();
    });
}

function downloadExample(lotteryType) {
    console.log('✅ downloadExample ejecutada para:', lotteryType);
    let csvContent = '';
    let filename = '';

    if (lotteryType === 'euromillones') {
        csvContent = 'date,num1,num2,num3,num4,num5,star1,star2,jackpot,winners\n';
        csvContent += '2024-01-01,5,12,23,34,45,3,8,15000000,0\n';
        csvContent += '2024-01-05,8,15,27,38,42,1,11,20000000,1\n';
        filename = 'ejemplo_euromillones.csv';
    } else {
        csvContent = 'date,num1,num2,num3,num4,num5,chance,jackpot,winners\n';
        csvContent += '2024-01-01,7,14,21,28,35,6,2000000,0\n';
        csvContent += '2024-01-04,3,16,22,33,41,2,3000000,1\n';
        filename = 'ejemplo_loto_francia.csv';
    }

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showAlert('success', 'Archivo de ejemplo descargado: ' + filename);
}

// ===== FUNCIONES DEL CONVERTIDOR DE FORMATOS =====
let convertedFileData = null;

function previewConversion() {
    console.log('✅ previewConversion ejecutada');

    const lotteryType = document.getElementById('converterLotteryType').value;
    const file = document.getElementById('converterFile').files[0];
    const inputFormat = document.getElementById('inputFormat').value;

    if (!lotteryType || !file) {
        showAlert('warning', 'Por favor selecciona el tipo de lotería y un archivo');
        return;
    }

    const previewArea = document.getElementById('previewArea');
    previewArea.innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-primary" role="status"></div>
            <p class="mt-3">Analizando archivo y generando vista previa...</p>
        </div>
    `;

    const formData = new FormData();
    formData.append('file', file);
    formData.append('lottery_type', lotteryType);
    formData.append('input_format', inputFormat);
    formData.append('preview_only', 'true');

    fetch('/api/convert_format', {
        method: 'POST',
        body: formData
    })
    .then(function(response) { return response.json(); })
    .then(function(data) {
        if (data.success) {
            let previewHtml = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle"></i> Vista Previa Generada</h6>
                    <p><strong>${data.total_rows}</strong> filas detectadas</p>
                    <p><small>Formato detectado: ${data.format_detected}</small></p>
                </div>

                <h6>Datos Originales (primeras 3 filas):</h6>
                <div class="table-responsive mb-3">
                    <table class="table table-sm table-bordered">
                        <thead class="table-dark">
                            <tr><th>Línea Original</th></tr>
                        </thead>
                        <tbody>
            `;

            data.original_sample.forEach(function(line, index) {
                previewHtml += `<tr><td><code>${line}</code></td></tr>`;
            });

            previewHtml += `
                        </tbody>
                    </table>
                </div>

                <h6>Datos Convertidos:</h6>
                <div class="table-responsive">
                    <table class="table table-sm table-bordered">
                        <thead class="table-success">
                            <tr>
            `;

            data.columns.forEach(function(col) {
                previewHtml += `<th>${col}</th>`;
            });

            previewHtml += `
                            </tr>
                        </thead>
                        <tbody>
            `;

            data.sample_data.forEach(function(row) {
                previewHtml += '<tr>';
                data.columns.forEach(function(col) {
                    previewHtml += `<td>${row[col] || ''}</td>`;
                });
                previewHtml += '</tr>';
            });

            previewHtml += `
                        </tbody>
                    </table>
                </div>
            `;

            previewArea.innerHTML = previewHtml;

            // Habilitar botón de conversión
            document.getElementById('convertBtn').disabled = false;

            showAlert('success', 'Vista previa generada exitosamente');
        } else {
            previewArea.innerHTML = `
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-triangle"></i> Error en Vista Previa</h6>
                    <p>${data.error}</p>
                    ${data.details ? `<p><small>${data.details}</small></p>` : ''}
                </div>
            `;
            showAlert('danger', 'Error generando vista previa: ' + data.error);
        }
    })
    .catch(function(error) {
        previewArea.innerHTML = `
            <div class="alert alert-danger">
                <h6><i class="fas fa-exclamation-triangle"></i> Error de Conexión</h6>
                <p>No se pudo conectar con el servidor: ${error.message}</p>
            </div>
        `;
        showAlert('danger', 'Error de conexión: ' + error.message);
    });
}

function executeConversion() {
    console.log('✅ executeConversion ejecutada');

    const lotteryType = document.getElementById('converterLotteryType').value;
    const file = document.getElementById('converterFile').files[0];
    const inputFormat = document.getElementById('inputFormat').value;

    if (!lotteryType || !file) {
        showAlert('warning', 'Por favor selecciona el tipo de lotería y un archivo');
        return;
    }

    const previewArea = document.getElementById('previewArea');
    previewArea.innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-success" role="status"></div>
            <p class="mt-3">Convirtiendo archivo completo...</p>
            <p class="text-muted">Esto puede tomar unos momentos.</p>
        </div>
    `;

    const formData = new FormData();
    formData.append('file', file);
    formData.append('lottery_type', lotteryType);
    formData.append('input_format', inputFormat);
    formData.append('preview_only', 'false');

    fetch('/api/convert_format', {
        method: 'POST',
        body: formData
    })
    .then(function(response) { return response.json(); })
    .then(function(data) {
        if (data.success) {
            convertedFileData = data;

            previewArea.innerHTML = `
                <div class="text-center">
                    <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                    <h5 class="text-success">¡Conversión Completada!</h5>
                    <p><strong>${data.total_rows}</strong> filas convertidas exitosamente</p>
                    <div class="alert alert-info mt-3">
                        <h6><i class="fas fa-file-alt"></i> Archivo Convertido</h6>
                        <p>Formato: ${data.format_detected}</p>
                        <p>Archivo: <code>${data.converted_file}</code></p>
                    </div>
                    <div class="d-grid gap-2 mt-3">
                        <button class="btn btn-primary" onclick="importConvertedFile()">
                            <i class="fas fa-upload"></i> Importar Datos Convertidos
                        </button>
                    </div>
                </div>
            `;

            // Habilitar botón de descarga
            document.getElementById('downloadBtn').disabled = false;

            showAlert('success', 'Archivo convertido exitosamente');
        } else {
            previewArea.innerHTML = `
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-triangle"></i> Error en Conversión</h6>
                    <p>${data.error}</p>
                    ${data.details ? `<p><small>${data.details}</small></p>` : ''}
                </div>
            `;
            showAlert('danger', 'Error en conversión: ' + data.error);
        }
    })
    .catch(function(error) {
        previewArea.innerHTML = `
            <div class="alert alert-danger">
                <h6><i class="fas fa-exclamation-triangle"></i> Error de Conexión</h6>
                <p>No se pudo conectar con el servidor: ${error.message}</p>
            </div>
        `;
        showAlert('danger', 'Error de conexión: ' + error.message);
    });
}

function downloadConvertedFile() {
    console.log('✅ downloadConvertedFile ejecutada');

    if (!convertedFileData) {
        showAlert('warning', 'No hay archivo convertido para descargar');
        return;
    }

    // Crear enlace de descarga
    const link = document.createElement('a');
    link.href = '/download_converted/' + encodeURIComponent(convertedFileData.converted_file);
    link.download = 'datos_convertidos.csv';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showAlert('success', 'Descargando archivo convertido...');
}

function importConvertedFile() {
    console.log('✅ importConvertedFile ejecutada');

    if (!convertedFileData) {
        showAlert('warning', 'No hay archivo convertido para importar');
        return;
    }

    // Cerrar modal del convertidor
    const modal = document.getElementById('formatConverterModal');
    const modalInstance = bootstrap.Modal.getInstance(modal);
    modalInstance.hide();

    // Importar automáticamente
    fetch('/api/import_converted_file', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            converted_file: convertedFileData.converted_file,
            lottery_type: convertedFileData.lottery_type
        })
    })
    .then(function(response) { return response.json(); })
    .then(function(data) {
        if (data.success) {
            showAlert('success', `¡Importación exitosa! ${data.imported_count} sorteos importados`);
            setTimeout(function() { checkCurrentData(); }, 2000);
        } else {
            showAlert('danger', 'Error en importación: ' + data.error);
        }
    })
    .catch(function(error) {
        showAlert('danger', 'Error de conexión: ' + error.message);
    });
}

// ===== FUNCIONALIDAD DEL FORMULARIO =====
function setupImportForm() {
    const form = document.getElementById('importForm');
    if (!form) return;

    form.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(form);
        const lotteryType = formData.get('lottery_type');
        const file = formData.get('file');

        if (!lotteryType || !file) {
            showAlert('warning', 'Por favor selecciona el tipo de lotería y un archivo');
            return;
        }

        // Mostrar estado de carga
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Importando...';
        submitBtn.disabled = true;

        showAlert('info', 'Iniciando importación de datos...');

        fetch('/upload_data', {
            method: 'POST',
            body: formData
        })
        .then(function(response) { return response.json(); })
        .then(function(data) {
            if (data.success) {
                showAlert('success', `¡Importación exitosa! ${data.imported_count} sorteos importados`);
                form.reset();
                setTimeout(function() { checkCurrentData(); }, 2000);

                // Mostrar detalles en el área de resultados
                const resultDiv = document.getElementById('importResult');
                resultDiv.innerHTML = `
                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle"></i> Importación Completada</h6>
                        <p><strong>${data.imported_count}</strong> sorteos importados exitosamente</p>
                        ${data.skipped_count > 0 ? `<p><small>Se omitieron ${data.skipped_count} duplicados</small></p>` : ''}
                        ${data.details ? `<p><small>${data.details}</small></p>` : ''}
                    </div>
                `;
                resultDiv.style.display = 'block';
            } else {
                showAlert('danger', 'Error en la importación: ' + data.error);

                const resultDiv = document.getElementById('importResult');
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle"></i> Error en la Importación</h6>
                        <p>${data.error}</p>
                        ${data.details ? `<p><small>${data.details}</small></p>` : ''}
                    </div>
                `;
                resultDiv.style.display = 'block';
            }
        })
        .catch(function(error) {
            showAlert('danger', 'Error de conexión: ' + error.message);

            const resultDiv = document.getElementById('importResult');
            resultDiv.innerHTML = `
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-triangle"></i> Error de Conexión</h6>
                    <p>No se pudo conectar con el servidor: ${error.message}</p>
                </div>
            `;
            resultDiv.style.display = 'block';
        })
        .finally(function() {
            // Restaurar botón
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    });
}

// ===== INICIALIZACIÓN =====
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Página cargada - Inicializando...');

    // Configurar formulario de importación
    setupImportForm();

    // Verificar estado inicial
    setTimeout(function() {
        checkCurrentData();
    }, 1000);

    // Test de funciones
    console.log('✅ Funciones disponibles:', {
        checkCurrentData: typeof checkCurrentData,
        scrapeRealData: typeof scrapeRealData,
        cleanDuplicates: typeof cleanDuplicates,
        showClearDataOptions: typeof showClearDataOptions,
        createBackup: typeof createBackup,
        showDataSources: typeof showDataSources,
        downloadExample: typeof downloadExample,
        setupImportForm: typeof setupImportForm,
        showFormatConverter: typeof showFormatConverter,
        previewConversion: typeof previewConversion,
        executeConversion: typeof executeConversion,
        downloadConvertedFile: typeof downloadConvertedFile,
        importConvertedFile: typeof importConvertedFile
    });

    console.log('✅ JavaScript cargado completamente');
});
</script>
{% endblock %}