<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Análisis - {{ lottery_type.title() }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .number-ball {
            display: inline-block;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            line-height: 35px;
            margin: 2px;
            font-weight: bold;
            font-size: 14px;
        }
        .star-ball {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .chance-ball {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .hot-number {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        }
        .cold-number {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line"></i> Sistema de Análisis de Loterías
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">
                    <i class="fas fa-home"></i> Inicio
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1>
                    <i class="fas fa-chart-bar"></i> 
                    Análisis de {{ 'Euromillones' if lottery_type == 'euromillones' else 'Loto Francia' }}
                </h1>
                <p class="text-muted">
                    Análisis estadístico basado en {{ frequencies.total_draws }} sorteos registrados.
                </p>
            </div>
        </div>

        <!-- Statistics Overview -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-fire"></i> Números Más Frecuentes
                        </h5>
                    </div>
                    <div class="card-body">
                        {% set sorted_main = frequencies.main_numbers.items() | sort(attribute='1', reverse=true) %}
                        {% for number, freq in sorted_main[:10] %}
                            <span class="number-ball hot-number" title="Frecuencia: {{ freq }}">{{ number }}</span>
                        {% endfor %}
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-snowflake"></i> Números Menos Frecuentes
                        </h5>
                    </div>
                    <div class="card-body">
                        {% set sorted_main = frequencies.main_numbers.items() | sort(attribute='1') %}
                        {% for number, freq in sorted_main[:10] %}
                            <span class="number-ball cold-number" title="Frecuencia: {{ freq }}">{{ number }}</span>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Frequency Charts -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-column"></i> Frecuencia de Números Principales
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="mainNumbersChart" height="100"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-column"></i> 
                            Frecuencia de {{ 'Estrellas' if lottery_type == 'euromillones' else 'Números Chance' }}
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="additionalNumbersChart" height="100"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Draws -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-history"></i> Sorteos Recientes
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Fecha</th>
                                        <th>Números Principales</th>
                                        <th>{{ 'Estrellas' if lottery_type == 'euromillones' else 'Chance' }}</th>
                                        <th>Bote</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for draw in recent_draws %}
                                    <tr>
                                        <td>{{ draw.draw_date }}</td>
                                        <td>
                                            {% for number in draw.main_numbers %}
                                                <span class="number-ball">{{ number }}</span>
                                            {% endfor %}
                                        </td>
                                        <td>
                                            {% for number in draw.additional_numbers %}
                                                <span class="number-ball {{ 'star-ball' if lottery_type == 'euromillones' else 'chance-ball' }}">{{ number }}</span>
                                            {% endfor %}
                                        </td>
                                        <td>
                                            {% if draw.jackpot_amount %}
                                                {{ "{:,.0f}".format(draw.jackpot_amount) }} €
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-bolt"></i> Acciones Rápidas
                        </h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary me-2" onclick="generatePrediction()">
                            <i class="fas fa-magic"></i> Generar Predicción
                        </button>
                        <button class="btn btn-outline-secondary me-2" onclick="refreshData()">
                            <i class="fas fa-sync-alt"></i> Actualizar Datos
                        </button>
                        <button class="btn btn-outline-info" onclick="downloadData()">
                            <i class="fas fa-download"></i> Descargar Análisis
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="bg-light mt-5 py-4">
        <div class="container">
            <div class="alert alert-warning">
                <h6><i class="fas fa-exclamation-triangle"></i> Aviso Importante</h6>
                <p class="mb-0 small">
                    Este análisis se basa en datos históricos y no garantiza resultados futuros. 
                    Las loterías son juegos de azar completamente aleatorios.
                </p>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Chart data
        const mainNumbersData = {{ frequencies.main_numbers | tojson }};
        const additionalNumbersData = {{ frequencies.additional_numbers | tojson }};
        const lotteryType = "{{ lottery_type }}";

        // Main numbers chart
        const mainCtx = document.getElementById('mainNumbersChart').getContext('2d');
        new Chart(mainCtx, {
            type: 'bar',
            data: {
                labels: Object.keys(mainNumbersData),
                datasets: [{
                    label: 'Frecuencia',
                    data: Object.values(mainNumbersData),
                    backgroundColor: 'rgba(102, 126, 234, 0.8)',
                    borderColor: 'rgba(102, 126, 234, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Frecuencia'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Números'
                        }
                    }
                }
            }
        });

        // Additional numbers chart
        const additionalCtx = document.getElementById('additionalNumbersChart').getContext('2d');
        new Chart(additionalCtx, {
            type: 'bar',
            data: {
                labels: Object.keys(additionalNumbersData),
                datasets: [{
                    label: 'Frecuencia',
                    data: Object.values(additionalNumbersData),
                    backgroundColor: lotteryType === 'euromillones' ? 'rgba(240, 147, 251, 0.8)' : 'rgba(79, 172, 254, 0.8)',
                    borderColor: lotteryType === 'euromillones' ? 'rgba(240, 147, 251, 1)' : 'rgba(79, 172, 254, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Frecuencia'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: lotteryType === 'euromillones' ? 'Estrellas' : 'Números Chance'
                        }
                    }
                }
            }
        });

        function generatePrediction() {
            fetch(`/generate_simple_prediction/${lotteryType}`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        alert('Error: ' + data.error);
                        return;
                    }
                    
                    alert(`Predicción generada:\nNúmeros principales: ${data.main_numbers.join(', ')}\n${lotteryType === 'euromillones' ? 'Estrellas' : 'Chance'}: ${data.additional_numbers.join(', ')}`);
                })
                .catch(error => {
                    alert('Error de conexión: ' + error.message);
                });
        }

        function refreshData() {
            location.reload();
        }

        function downloadData() {
            const data = {
                lottery_type: lotteryType,
                frequencies: {
                    main_numbers: mainNumbersData,
                    additional_numbers: additionalNumbersData
                },
                generated_at: new Date().toISOString()
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `analisis_${lotteryType}_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>
