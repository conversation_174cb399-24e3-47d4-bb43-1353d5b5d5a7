"""
Data import module for lottery historical data
"""
import pandas as pd
import numpy as np
from datetime import datetime
import os
import logging
from models import db, LotteryDraw
from config import Config

logger = logging.getLogger(__name__)

class DataImporter:
    """Import lottery data from various file formats"""
    
    def __init__(self):
        self.supported_formats = ['csv', 'txt', 'xlsx']
        self.validation_errors = []
    
    def validate_file(self, file_path):
        """Validate file format and existence"""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        file_extension = file_path.split('.')[-1].lower()
        if file_extension not in self.supported_formats:
            raise ValueError(f"Unsupported file format: {file_extension}")
        
        return file_extension
    
    def read_file(self, file_path, lottery_type):
        """Read data from file based on format"""
        file_format = self.validate_file(file_path)
        
        try:
            if file_format == 'csv':
                df = pd.read_csv(file_path)
            elif file_format == 'txt':
                # Try different separators
                try:
                    df = pd.read_csv(file_path, sep='\t')
                except:
                    try:
                        df = pd.read_csv(file_path, sep=';')
                    except:
                        df = pd.read_csv(file_path, sep=' ')
            elif file_format == 'xlsx':
                df = pd.read_excel(file_path)
            
            return self.process_dataframe(df, lottery_type)
            
        except Exception as e:
            logger.error(f"Error reading file {file_path}: {e}")
            raise
    
    def process_dataframe(self, df, lottery_type):
        """Process and validate dataframe"""
        self.validation_errors = []
        processed_data = []
        
        # Get lottery configuration
        config = Config.EUROMILLONES_CONFIG if lottery_type == 'euromillones' else Config.LOTO_FRANCE_CONFIG
        main_config = config['main_numbers']
        additional_config = config.get('stars', config.get('chance'))
        
        # Try to identify columns
        columns = self.identify_columns(df, lottery_type)
        if not columns:
            raise ValueError("Could not identify required columns in the data")
        
        for index, row in df.iterrows():
            try:
                # Parse date
                date_value = row[columns['date']]
                draw_date = self.parse_date(date_value)
                if not draw_date:
                    self.validation_errors.append(f"Row {index + 1}: Invalid date format")
                    continue
                
                # Parse main numbers
                main_numbers = self.parse_numbers(row, columns['main_numbers'], main_config)
                if not main_numbers:
                    self.validation_errors.append(f"Row {index + 1}: Invalid main numbers")
                    continue
                
                # Parse additional numbers
                additional_numbers = self.parse_numbers(row, columns['additional_numbers'], additional_config)
                if not additional_numbers:
                    self.validation_errors.append(f"Row {index + 1}: Invalid additional numbers")
                    continue
                
                # Validate number ranges
                if not self.validate_number_ranges(main_numbers, main_config):
                    self.validation_errors.append(f"Row {index + 1}: Main numbers out of range")
                    continue
                
                if not self.validate_number_ranges(additional_numbers, additional_config):
                    self.validation_errors.append(f"Row {index + 1}: Additional numbers out of range")
                    continue
                
                processed_data.append({
                    'date': draw_date,
                    'main_numbers': main_numbers,
                    'additional_numbers': additional_numbers,
                    'jackpot': self.parse_jackpot(row, columns.get('jackpot')),
                    'winners': self.parse_winners(row, columns.get('winners'))
                })
                
            except Exception as e:
                self.validation_errors.append(f"Row {index + 1}: {str(e)}")
                continue
        
        return processed_data
    
    def identify_columns(self, df, lottery_type):
        """Identify column mappings based on content and headers"""
        columns = {}
        
        # Common date column names
        date_patterns = ['date', 'fecha', 'datum', 'draw_date', 'sorteo']
        for col in df.columns:
            if any(pattern in col.lower() for pattern in date_patterns):
                columns['date'] = col
                break
        
        # Try to identify number columns
        if lottery_type == 'euromillones':
            # Look for 5 main numbers and 2 stars
            number_cols = [col for col in df.columns if 'num' in col.lower() or col.lower().startswith('n')]
            star_cols = [col for col in df.columns if 'star' in col.lower() or 'estrella' in col.lower()]
            
            if len(number_cols) >= 5:
                columns['main_numbers'] = number_cols[:5]
            if len(star_cols) >= 2:
                columns['additional_numbers'] = star_cols[:2]
        
        elif lottery_type == 'loto_france':
            # Look for 5 main numbers and 1 chance
            number_cols = [col for col in df.columns if 'num' in col.lower() or col.lower().startswith('n')]
            chance_cols = [col for col in df.columns if 'chance' in col.lower()]
            
            if len(number_cols) >= 5:
                columns['main_numbers'] = number_cols[:5]
            if len(chance_cols) >= 1:
                columns['additional_numbers'] = chance_cols[:1]
        
        # Optional columns
        jackpot_patterns = ['jackpot', 'premio', 'bote']
        for col in df.columns:
            if any(pattern in col.lower() for pattern in jackpot_patterns):
                columns['jackpot'] = col
                break
        
        winners_patterns = ['winners', 'ganadores', 'acertantes']
        for col in df.columns:
            if any(pattern in col.lower() for pattern in winners_patterns):
                columns['winners'] = col
                break
        
        # Check if we have minimum required columns
        required = ['date', 'main_numbers', 'additional_numbers']
        if all(key in columns for key in required):
            return columns
        
        return None
    
    def parse_date(self, date_value):
        """Parse date from various formats"""
        if pd.isna(date_value):
            return None
        
        date_formats = [
            '%Y-%m-%d', '%d/%m/%Y', '%m/%d/%Y', '%d-%m-%Y',
            '%Y/%m/%d', '%d.%m.%Y', '%Y.%m.%d'
        ]
        
        date_str = str(date_value).strip()
        
        for fmt in date_formats:
            try:
                return datetime.strptime(date_str, fmt).date()
            except ValueError:
                continue
        
        return None
    
    def parse_numbers(self, row, number_columns, config):
        """Parse numbers from row data"""
        numbers = []
        
        if isinstance(number_columns, list):
            # Multiple columns for numbers
            for col in number_columns:
                try:
                    num = int(row[col])
                    numbers.append(num)
                except (ValueError, TypeError):
                    return None
        else:
            # Single column with comma-separated numbers
            try:
                number_str = str(row[number_columns]).strip()
                # Try different separators
                for sep in [',', ';', ' ', '-']:
                    if sep in number_str:
                        parts = number_str.split(sep)
                        numbers = [int(part.strip()) for part in parts if part.strip()]
                        break
                else:
                    # Single number
                    numbers = [int(number_str)]
            except (ValueError, TypeError):
                return None
        
        # Check count
        if len(numbers) != config['count']:
            return None
        
        return numbers
    
    def validate_number_ranges(self, numbers, config):
        """Validate that numbers are within allowed range"""
        for num in numbers:
            if num < config['min'] or num > config['max']:
                return False
        return True
    
    def parse_jackpot(self, row, jackpot_column):
        """Parse jackpot amount"""
        if not jackpot_column or pd.isna(row[jackpot_column]):
            return None
        
        try:
            # Remove currency symbols and convert to float
            jackpot_str = str(row[jackpot_column]).replace('€', '').replace('$', '').replace(',', '')
            return float(jackpot_str)
        except (ValueError, TypeError):
            return None
    
    def parse_winners(self, row, winners_column):
        """Parse number of winners"""
        if not winners_column or pd.isna(row[winners_column]):
            return None
        
        try:
            return int(row[winners_column])
        except (ValueError, TypeError):
            return None
    
    def import_to_database(self, file_path, lottery_type):
        """Import data from file to database"""
        try:
            processed_data = self.read_file(file_path, lottery_type)
            
            if not processed_data:
                return {
                    'success': False,
                    'message': 'No valid data found in file',
                    'errors': self.validation_errors
                }
            
            # Save to database
            saved_count = 0
            duplicate_count = 0
            
            for data in processed_data:
                # Check if draw already exists
                existing = LotteryDraw.query.filter_by(
                    lottery_type=lottery_type,
                    draw_date=data['date']
                ).first()
                
                if existing:
                    duplicate_count += 1
                    continue
                
                draw = LotteryDraw(
                    lottery_type=lottery_type,
                    draw_date=data['date'],
                    main_numbers=data['main_numbers'],
                    additional_numbers=data['additional_numbers'],
                    jackpot_amount=data['jackpot'],
                    winners_count=data['winners']
                )
                
                db.session.add(draw)
                saved_count += 1
            
            db.session.commit()
            
            return {
                'success': True,
                'message': f'Successfully imported {saved_count} draws',
                'saved_count': saved_count,
                'duplicate_count': duplicate_count,
                'errors': self.validation_errors
            }
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error importing data: {e}")
            return {
                'success': False,
                'message': f'Import failed: {str(e)}',
                'errors': self.validation_errors
            }
