{% extends "base.html" %}

{% block title %}Visualizaciones Avanzadas - {{ lottery_type.title() }}{% endblock %}

{% block extra_css %}
<script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
<style>
    .visualization-card {
        margin-bottom: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .viz-image {
        max-width: 100%;
        height: auto;
        border-radius: 8px;
    }
    .download-btn {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 10;
    }
    .viz-container {
        position: relative;
    }
    .loading-viz {
        text-align: center;
        padding: 3rem;
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1>
            <i class="fas fa-chart-area"></i> 
            Visualizaciones Avanzadas - {{ 'Euromillones' if lottery_type == 'euromillones' else 'Loto Francia' }}
        </h1>
        <p class="text-muted">
            Análisis visual avanzado con mapas de calor, correlaciones y tendencias temporales.
        </p>
    </div>
</div>

<!-- Controls -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-sliders-h"></i> Controles de Visualización
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label for="vizYears" class="form-label">Período de Análisis</label>
                        <select class="form-select" id="vizYears">
                            <option value="1" {{ 'selected' if years == 1 }}>1 año</option>
                            <option value="2" {{ 'selected' if years == 2 }}>2 años</option>
                            <option value="5" {{ 'selected' if years == 5 }}>5 años</option>
                            <option value="10" {{ 'selected' if years == 10 }}>10 años</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button class="btn btn-primary" onclick="updateVisualizations()">
                                <i class="fas fa-sync-alt"></i> Actualizar
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button class="btn btn-success" onclick="exportToPDF()">
                                <i class="fas fa-file-pdf"></i> Exportar PDF
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button class="btn btn-info" onclick="toggleFullscreen()">
                                <i class="fas fa-expand"></i> Pantalla Completa
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Frequency Heatmap -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card visualization-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-fire"></i> Mapa de Calor - Frecuencias de Números
                </h5>
                <button class="btn btn-sm btn-outline-primary download-btn" onclick="downloadImage('frequency_heatmap')">
                    <i class="fas fa-download"></i>
                </button>
            </div>
            <div class="card-body">
                <div class="viz-container">
                    {% if visualizations.frequency_heatmap %}
                        <img src="data:image/png;base64,{{ visualizations.frequency_heatmap }}" 
                             class="viz-image" alt="Mapa de Calor de Frecuencias" id="frequency_heatmap">
                    {% else %}
                        <div class="loading-viz">
                            <i class="fas fa-spinner fa-spin fa-2x"></i>
                            <p class="mt-2">Generando mapa de calor...</p>
                        </div>
                    {% endif %}
                </div>
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i>
                        Este mapa muestra la frecuencia de aparición de cada número. Los colores más intensos indican mayor frecuencia.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Interactive Frequency Chart -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card visualization-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar"></i> Gráfico Interactivo de Frecuencias
                </h5>
            </div>
            <div class="card-body">
                <div id="interactive-frequency-chart"></div>
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="fas fa-mouse-pointer"></i>
                        Pasa el cursor sobre las barras para ver detalles. Puedes hacer zoom y desplazarte por el gráfico.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Correlation Heatmap -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card visualization-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-project-diagram"></i> Mapa de Correlación entre Números
                </h5>
                <button class="btn btn-sm btn-outline-primary download-btn" onclick="downloadImage('correlation_heatmap')">
                    <i class="fas fa-download"></i>
                </button>
            </div>
            <div class="card-body">
                <div class="viz-container">
                    {% if visualizations.correlation_heatmap %}
                        <img src="data:image/png;base64,{{ visualizations.correlation_heatmap }}" 
                             class="viz-image" alt="Mapa de Correlación" id="correlation_heatmap">
                    {% else %}
                        <div class="loading-viz">
                            <i class="fas fa-spinner fa-spin fa-2x"></i>
                            <p class="mt-2">Generando mapa de correlación...</p>
                        </div>
                    {% endif %}
                </div>
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i>
                        Este mapa muestra las correlaciones entre números que aparecen juntos en los sorteos.
                        Los colores cálidos indican correlación positiva, los fríos correlación negativa.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Temporal Trends -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card visualization-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line"></i> Análisis de Tendencias Temporales
                </h5>
                <button class="btn btn-sm btn-outline-primary download-btn" onclick="downloadImage('temporal_trends')">
                    <i class="fas fa-download"></i>
                </button>
            </div>
            <div class="card-body">
                <div class="viz-container">
                    {% if visualizations.temporal_trends %}
                        <img src="data:image/png;base64,{{ visualizations.temporal_trends }}" 
                             class="viz-image" alt="Tendencias Temporales" id="temporal_trends">
                    {% else %}
                        <div class="loading-viz">
                            <i class="fas fa-spinner fa-spin fa-2x"></i>
                            <p class="mt-2">Generando análisis temporal...</p>
                        </div>
                    {% endif %}
                </div>
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i>
                        Estos gráficos muestran la evolución temporal de diferentes métricas: suma de números, 
                        cantidad de pares y números consecutivos a lo largo del tiempo.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Pattern Analysis -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card visualization-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-puzzle-piece"></i> Análisis de Patrones
                </h5>
                <button class="btn btn-sm btn-outline-primary download-btn" onclick="downloadImage('pattern_analysis')">
                    <i class="fas fa-download"></i>
                </button>
            </div>
            <div class="card-body">
                <div class="viz-container">
                    {% if visualizations.pattern_analysis %}
                        <img src="data:image/png;base64,{{ visualizations.pattern_analysis }}" 
                             class="viz-image" alt="Análisis de Patrones" id="pattern_analysis">
                    {% else %}
                        <div class="loading-viz">
                            <i class="fas fa-spinner fa-spin fa-2x"></i>
                            <p class="mt-2">Generando análisis de patrones...</p>
                        </div>
                    {% endif %}
                </div>
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i>
                        Análisis de diferentes patrones: distribución par/impar, números consecutivos, 
                        distribución de sumas y parejas de números más frecuentes.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Educational Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-graduation-cap"></i> Guía de Interpretación
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-fire"></i> Mapa de Calor de Frecuencias</h6>
                        <p class="small">
                            Muestra qué números han salido más frecuentemente. Los números "calientes" 
                            aparecen en rojo/naranja, mientras que los "fríos" en azul/verde. 
                            <strong>Importante:</strong> En una lotería verdaderamente aleatoria, 
                            todos los números tienen la misma probabilidad en cada sorteo.
                        </p>
                        
                        <h6><i class="fas fa-project-diagram"></i> Mapa de Correlación</h6>
                        <p class="small">
                            Analiza si ciertos números tienden a aparecer juntos más frecuentemente. 
                            Las correlaciones fuertes pueden indicar patrones interesantes, pero 
                            recuerda que en un juego aleatorio, estas correlaciones pueden ser casuales.
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-chart-line"></i> Tendencias Temporales</h6>
                        <p class="small">
                            Examina cómo han evolucionado diferentes métricas a lo largo del tiempo. 
                            Puede revelar ciclos o tendencias, aunque en una lotería aleatoria, 
                            estas tendencias no predicen resultados futuros.
                        </p>
                        
                        <h6><i class="fas fa-puzzle-piece"></i> Análisis de Patrones</h6>
                        <p class="small">
                            Estudia la distribución de diferentes características como números pares/impares, 
                            consecutivos y sumas. Ayuda a entender la "forma" típica de los sorteos, 
                            pero no garantiza que los patrones se repitan.
                        </p>
                    </div>
                </div>
                <div class="alert alert-warning mt-3">
                    <h6><i class="fas fa-exclamation-triangle"></i> Recordatorio Importante</h6>
                    <p class="mb-0">
                        Todas estas visualizaciones son herramientas de análisis estadístico para fines educativos. 
                        Las loterías son juegos de azar completamente aleatorios, y los patrones históricos 
                        no influyen en los resultados futuros. Cada sorteo es independiente de los anteriores.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    const lotteryType = "{{ lottery_type }}";
    
    // Load interactive frequency chart
    document.addEventListener('DOMContentLoaded', function() {
        {% if visualizations.interactive_frequency %}
            const plotData = {{ visualizations.interactive_frequency | safe }};
            Plotly.newPlot('interactive-frequency-chart', plotData.data, plotData.layout, {responsive: true});
        {% endif %}
    });

    function updateVisualizations() {
        const years = document.getElementById('vizYears').value;
        window.location.href = `/visualizations/${lotteryType}?years=${years}`;
    }

    function exportToPDF() {
        showAlert('info', 'Generando PDF, por favor espera...');
        
        const years = document.getElementById('vizYears').value;
        fetch(`/api/export_visualizations/${lotteryType}?years=${years}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', 'PDF generado correctamente');
                    // Create download link
                    const link = document.createElement('a');
                    link.href = data.download_url;
                    link.download = data.filename;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                } else {
                    showAlert('danger', 'Error generando PDF: ' + data.error);
                }
            })
            .catch(error => {
                showAlert('danger', 'Error de conexión: ' + error.message);
            });
    }

    function downloadImage(imageId) {
        const img = document.getElementById(imageId);
        if (img) {
            const link = document.createElement('a');
            link.href = img.src;
            link.download = `${imageId}_${lotteryType}.png`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    }

    function toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen();
        } else {
            if (document.exitFullscreen) {
                document.exitFullscreen();
            }
        }
    }

    // Add zoom functionality to images
    document.querySelectorAll('.viz-image').forEach(img => {
        img.addEventListener('click', function() {
            if (this.style.transform === 'scale(1.5)') {
                this.style.transform = 'scale(1)';
                this.style.cursor = 'zoom-in';
            } else {
                this.style.transform = 'scale(1.5)';
                this.style.cursor = 'zoom-out';
            }
        });
        img.style.cursor = 'zoom-in';
        img.style.transition = 'transform 0.3s ease';
    });
</script>
{% endblock %}
