"""
Configuration settings for the Lottery Analysis System
"""
import os

class Config:
    # Database configuration
    import os
    BASE_DIR = os.path.abspath(os.path.dirname(__file__))
    DATABASE_DIR = os.path.join(BASE_DIR, 'database')
    os.makedirs(DATABASE_DIR, exist_ok=True)
    SQLALCHEMY_DATABASE_URI = f'sqlite:///{os.path.join(DATABASE_DIR, "lottery.db")}'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Application settings
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'lottery-analysis-secret-key-2025'
    DEBUG = True
    
    # Data sources
    EUROMILLONES_URL = 'https://www.loteriasyapuestas.es/es/euromillones/estadisticas/numeros-que-mas-salen'
    LOTO_FRANCE_URL = 'https://www.fdj.fr/jeux-de-tirage/loto/statistiques'
    
    # Lottery configurations
    EUROMILLONES_CONFIG = {
        'main_numbers': {'min': 1, 'max': 50, 'count': 5},
        'stars': {'min': 1, 'max': 12, 'count': 2},
        'name': 'Euromillones'
    }
    
    LOTO_FRANCE_CONFIG = {
        'main_numbers': {'min': 1, 'max': 49, 'count': 5},
        'chance': {'min': 1, 'max': 10, 'count': 1},
        'name': 'Loto France'
    }
    
    # Analysis settings
    DEFAULT_ANALYSIS_YEARS = 10
    DEFAULT_COMBINATIONS_COUNT = 10
    MIN_HISTORICAL_DATA_YEARS = 1
    MAX_HISTORICAL_DATA_YEARS = 20
    
    # File upload settings
    UPLOAD_FOLDER = 'uploads'
    ALLOWED_EXTENSIONS = {'txt', 'csv', 'xlsx'}
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size
    
    # Machine learning settings
    NEURAL_NETWORK_EPOCHS = 100
    MARKOV_CHAIN_ORDER = 3
    
    # Visualization settings
    CHART_COLORS = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8']
    
    @staticmethod
    def init_app(app):
        pass

class DevelopmentConfig(Config):
    DEBUG = True

class ProductionConfig(Config):
    DEBUG = False

config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
