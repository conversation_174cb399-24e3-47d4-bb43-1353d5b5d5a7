{% extends "base.html" %}

{% block title %}Inicio - Sistema de Análisis de Loterías{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="jumbotron bg-primary text-white p-5 rounded mb-4">
            <h1 class="display-4">
                <i class="fas fa-chart-line"></i> Sistema de Análisis de Loterías
            </h1>
            <p class="lead">
                Análisis estadístico avanzado y predicciones para Euromillones y Loto Francia
            </p>
            <hr class="my-4" style="border-color: rgba(255,255,255,0.3);">
            <p>
                Utiliza modelos matemáticos, cadenas de Markov y redes neuronales para analizar 
                patrones históricos y generar predicciones optimizadas.
            </p>
        </div>
    </div>
</div>

<!-- Statistics Overview -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-star"></i> Euromillones
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <h3 class="text-primary">{{ euromillones_count }}</h3>
                        <p class="text-muted">Sorteos registrados</p>
                    </div>
                    <div class="col-6">
                        {% if latest_euromillones %}
                            <p class="mb-1"><strong>Último sorteo:</strong></p>
                            <p class="mb-1">{{ latest_euromillones.draw_date.strftime('%d/%m/%Y') }}</p>
                            <div class="mb-2">
                                {% for number in latest_euromillones.get_main_numbers() %}
                                    <span class="number-ball">{{ number }}</span>
                                {% endfor %}
                                {% for star in latest_euromillones.get_additional_numbers() %}
                                    <span class="number-ball star-ball">{{ star }}</span>
                                {% endfor %}
                            </div>
                        {% else %}
                            <p class="text-muted">No hay datos disponibles</p>
                        {% endif %}
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{{ url_for('lottery_analysis', lottery_type='euromillones') }}" class="btn btn-primary me-2">
                        <i class="fas fa-chart-bar"></i> Análisis
                    </a>
                    <a href="{{ url_for('predictions', lottery_type='euromillones') }}" class="btn btn-outline-primary">
                        <i class="fas fa-crystal-ball"></i> Predicciones
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-clover"></i> Loto Francia
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <h3 class="text-info">{{ loto_france_count }}</h3>
                        <p class="text-muted">Sorteos registrados</p>
                    </div>
                    <div class="col-6">
                        {% if latest_loto_france %}
                            <p class="mb-1"><strong>Último sorteo:</strong></p>
                            <p class="mb-1">{{ latest_loto_france.draw_date.strftime('%d/%m/%Y') }}</p>
                            <div class="mb-2">
                                {% for number in latest_loto_france.get_main_numbers() %}
                                    <span class="number-ball">{{ number }}</span>
                                {% endfor %}
                                {% for chance in latest_loto_france.get_additional_numbers() %}
                                    <span class="number-ball chance-ball">{{ chance }}</span>
                                {% endfor %}
                            </div>
                        {% else %}
                            <p class="text-muted">No hay datos disponibles</p>
                        {% endif %}
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{{ url_for('lottery_analysis', lottery_type='loto_france') }}" class="btn btn-info me-2">
                        <i class="fas fa-chart-bar"></i> Análisis
                    </a>
                    <a href="{{ url_for('predictions', lottery_type='loto_france') }}" class="btn btn-outline-info">
                        <i class="fas fa-crystal-ball"></i> Predicciones
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Features Overview -->
<div class="row mb-4">
    <div class="col-12">
        <h2 class="mb-4">Características del Sistema</h2>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-chart-line fa-3x text-primary mb-3"></i>
                <h5>Análisis Estadístico</h5>
                <p class="text-muted">
                    Frecuencias de números, patrones combinatorios, 
                    distribuciones par/impar y análisis de tendencias.
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-brain fa-3x text-success mb-3"></i>
                <h5>Modelos Avanzados</h5>
                <p class="text-muted">
                    Cadenas de Markov y redes neuronales para identificar 
                    patrones complejos en los datos históricos.
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-sync-alt fa-3x text-info mb-3"></i>
                <h5>Actualización Automática</h5>
                <p class="text-muted">
                    Conexión automática a fuentes oficiales para 
                    mantener los datos siempre actualizados.
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt"></i> Acciones Rápidas
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-primary w-100" onclick="updateData()">
                            <i class="fas fa-sync-alt"></i> Actualizar Datos
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-warning w-100" onclick="loadHistoricalData()">
                            <i class="fas fa-database"></i> Cargar Datos Reales
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('import_data_page') }}" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-upload"></i> Importar Datos
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-success w-100" onclick="generateQuickPredictions()">
                            <i class="fas fa-magic"></i> Predicción Rápida
                        </button>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('settings') }}" class="btn btn-outline-info w-100">
                            <i class="fas fa-cog"></i> Configuración
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-dark w-100" onclick="checkDataStatus()">
                            <i class="fas fa-info-circle"></i> Estado de Datos
                        </button>
                    </div>
                    <div class="col-md-6 mb-2">
                        <div class="alert alert-info mb-0 py-2">
                            <small><i class="fas fa-lightbulb"></i>
                            <strong>Tip:</strong> Carga datos históricos reales para análisis más precisos
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Information -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle"></i> Información del Sistema
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success"></i> Análisis de frecuencias</li>
                    <li><i class="fas fa-check text-success"></i> Cálculos de probabilidad</li>
                    <li><i class="fas fa-check text-success"></i> Patrones combinatorios</li>
                    <li><i class="fas fa-check text-success"></i> Cadenas de Markov</li>
                    <li><i class="fas fa-check text-success"></i> Redes neuronales</li>
                    <li><i class="fas fa-check text-success"></i> Visualizaciones interactivas</li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-question-circle"></i> Cómo Usar el Sistema
                </h5>
            </div>
            <div class="card-body">
                <ol class="mb-0">
                    <li>Actualiza los datos desde fuentes oficiales</li>
                    <li>Explora el análisis estadístico de cada lotería</li>
                    <li>Revisa los patrones y frecuencias históricas</li>
                    <li>Genera predicciones usando diferentes modelos</li>
                    <li>Configura parámetros según tus preferencias</li>
                    <li>Importa datos adicionales si es necesario</li>
                </ol>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function generateQuickPredictions() {
    // Show modal to select lottery type
    const modal = `
        <div class="modal fade" id="quickPredictionModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Predicción Rápida</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p>Selecciona la lotería para generar predicciones:</p>
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" onclick="generatePrediction('euromillones')">
                                <i class="fas fa-star"></i> Euromillones
                            </button>
                            <button class="btn btn-info" onclick="generatePrediction('loto_france')">
                                <i class="fas fa-clover"></i> Loto Francia
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', modal);
    const modalElement = new bootstrap.Modal(document.getElementById('quickPredictionModal'));
    modalElement.show();
    
    // Clean up modal after hiding
    document.getElementById('quickPredictionModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function generatePrediction(lotteryType) {
    // Close modal
    bootstrap.Modal.getInstance(document.getElementById('quickPredictionModal')).hide();

    // Redirect to predictions page
    window.location.href = `/predictions/${lotteryType}`;
}

function loadHistoricalData() {
    // Show modal to select years
    const modal = `
        <div class="modal fade" id="loadDataModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-database"></i> Cargar Datos Históricos Reales
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Importante:</strong> Esta acción cargará datos históricos reales y puede tomar varios minutos.
                        </div>
                        <div class="mb-3">
                            <label for="yearsSelect" class="form-label">Años de datos a cargar:</label>
                            <select class="form-select" id="yearsSelect">
                                <option value="1">1 año (más rápido)</option>
                                <option value="2" selected>2 años (recomendado)</option>
                                <option value="3">3 años</option>
                                <option value="5">5 años (más completo)</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i>
                                Se cargarán datos históricos para Euromillones y Loto Francia.
                                Los datos existentes no se duplicarán.
                            </small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="button" class="btn btn-warning" onclick="executeLoadData()">
                            <i class="fas fa-download"></i> Cargar Datos
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modal);
    const modalElement = new bootstrap.Modal(document.getElementById('loadDataModal'));
    modalElement.show();

    // Clean up modal after hiding
    document.getElementById('loadDataModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function executeLoadData() {
    const years = document.getElementById('yearsSelect').value;
    const modal = bootstrap.Modal.getInstance(document.getElementById('loadDataModal'));

    // Show loading state
    const modalBody = document.querySelector('#loadDataModal .modal-body');
    modalBody.innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-warning" role="status">
                <span class="visually-hidden">Cargando...</span>
            </div>
            <p class="mt-3">Cargando datos históricos...</p>
            <p class="text-muted">Esto puede tomar varios minutos. Por favor, no cierres esta ventana.</p>
        </div>
    `;

    // Disable close buttons
    document.querySelector('#loadDataModal .btn-close').disabled = true;
    document.querySelector('#loadDataModal .modal-footer').style.display = 'none';

    // Make request
    fetch(`/load_historical_data?years=${years}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                modalBody.innerHTML = `
                    <div class="text-center">
                        <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                        <h5 class="text-success">¡Datos Cargados Exitosamente!</h5>
                        <p>Se cargaron <strong>${data.total_saved}</strong> sorteos históricos de ${data.years_loaded} años.</p>
                        <p class="text-muted">Los datos están listos para análisis.</p>
                    </div>
                `;

                // Show close button
                document.querySelector('#loadDataModal .modal-footer').innerHTML = `
                    <button type="button" class="btn btn-success" data-bs-dismiss="modal">
                        <i class="fas fa-check"></i> Continuar
                    </button>
                `;
                document.querySelector('#loadDataModal .modal-footer').style.display = 'block';

                // Refresh page after modal closes
                document.getElementById('loadDataModal').addEventListener('hidden.bs.modal', function() {
                    location.reload();
                });
            } else {
                modalBody.innerHTML = `
                    <div class="text-center">
                        <i class="fas fa-exclamation-triangle text-danger fa-3x mb-3"></i>
                        <h5 class="text-danger">Error al Cargar Datos</h5>
                        <p>Error: ${data.error}</p>
                        <p class="text-muted">Por favor, inténtalo de nuevo más tarde.</p>
                    </div>
                `;

                document.querySelector('#loadDataModal .modal-footer').innerHTML = `
                    <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Cerrar</button>
                `;
                document.querySelector('#loadDataModal .modal-footer').style.display = 'block';
            }
        })
        .catch(error => {
            modalBody.innerHTML = `
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle text-danger fa-3x mb-3"></i>
                    <h5 class="text-danger">Error de Conexión</h5>
                    <p>No se pudo conectar con el servidor.</p>
                    <p class="text-muted">Error: ${error.message}</p>
                </div>
            `;

            document.querySelector('#loadDataModal .modal-footer').innerHTML = `
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">Cerrar</button>
            `;
            document.querySelector('#loadDataModal .modal-footer').style.display = 'block';
        });
}

function checkDataStatus() {
    // Show loading
    showAlert('info', 'Verificando estado de los datos...');

    fetch('/api/data_status')
        .then(response => response.json())
        .then(data => {
            const modal = `
                <div class="modal fade" id="dataStatusModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <i class="fas fa-info-circle"></i> Estado de los Datos
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header bg-primary text-white">
                                                <h6 class="mb-0"><i class="fas fa-star"></i> Euromillones</h6>
                                            </div>
                                            <div class="card-body">
                                                <p><strong>Sorteos:</strong> ${data.euromillones.count}</p>
                                                <p><strong>Desde:</strong> ${data.euromillones.date_range.from || 'N/A'}</p>
                                                <p><strong>Hasta:</strong> ${data.euromillones.date_range.to || 'N/A'}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header bg-info text-white">
                                                <h6 class="mb-0"><i class="fas fa-clover"></i> Loto Francia</h6>
                                            </div>
                                            <div class="card-body">
                                                <p><strong>Sorteos:</strong> ${data.loto_france.count}</p>
                                                <p><strong>Desde:</strong> ${data.loto_france.date_range.from || 'N/A'}</p>
                                                <p><strong>Hasta:</strong> ${data.loto_france.date_range.to || 'N/A'}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <div class="alert alert-info">
                                        <h6><i class="fas fa-chart-bar"></i> Resumen Total</h6>
                                        <p class="mb-0">Total de sorteos en la base de datos: <strong>${data.total_draws}</strong></p>
                                    </div>
                                </div>
                                ${data.total_draws < 100 ? `
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        <strong>Recomendación:</strong> Para análisis más precisos, considera cargar más datos históricos.
                                    </div>
                                ` : ''}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
                                ${data.total_draws < 500 ? `
                                    <button type="button" class="btn btn-warning" onclick="loadHistoricalData(); bootstrap.Modal.getInstance(document.getElementById('dataStatusModal')).hide();">
                                        <i class="fas fa-database"></i> Cargar Más Datos
                                    </button>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modal);
            const modalElement = new bootstrap.Modal(document.getElementById('dataStatusModal'));
            modalElement.show();

            // Clean up modal after hiding
            document.getElementById('dataStatusModal').addEventListener('hidden.bs.modal', function() {
                this.remove();
            });
        })
        .catch(error => {
            showAlert('danger', 'Error al verificar el estado de los datos: ' + error.message);
        });
}
</script>
{% endblock %}
