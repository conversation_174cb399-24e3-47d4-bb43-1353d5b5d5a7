{% extends "base.html" %}

{% block title %}Inicio - Sistema de Análisis de Loterías{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="jumbotron bg-primary text-white p-5 rounded mb-4">
            <h1 class="display-4">
                <i class="fas fa-chart-line"></i> Sistema de Análisis de Loterías
            </h1>
            <p class="lead">
                Análisis estadístico avanzado y predicciones para Euromillones y Loto Francia
            </p>
            <hr class="my-4" style="border-color: rgba(255,255,255,0.3);">
            <p>
                Utiliza modelos matemáticos, cadenas de Markov y redes neuronales para analizar 
                patrones históricos y generar predicciones optimizadas.
            </p>
        </div>
    </div>
</div>

<!-- Statistics Overview -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-star"></i> Euromillones
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <h3 class="text-primary">{{ euromillones_count }}</h3>
                        <p class="text-muted">Sorteos registrados</p>
                    </div>
                    <div class="col-6">
                        {% if latest_euromillones %}
                            <p class="mb-1"><strong>Último sorteo:</strong></p>
                            <p class="mb-1">{{ latest_euromillones.draw_date.strftime('%d/%m/%Y') }}</p>
                            <div class="mb-2">
                                {% for number in latest_euromillones.get_main_numbers() %}
                                    <span class="number-ball">{{ number }}</span>
                                {% endfor %}
                                {% for star in latest_euromillones.get_additional_numbers() %}
                                    <span class="number-ball star-ball">{{ star }}</span>
                                {% endfor %}
                            </div>
                        {% else %}
                            <p class="text-muted">No hay datos disponibles</p>
                        {% endif %}
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{{ url_for('lottery_analysis', lottery_type='euromillones') }}" class="btn btn-primary me-2">
                        <i class="fas fa-chart-bar"></i> Análisis
                    </a>
                    <a href="{{ url_for('predictions', lottery_type='euromillones') }}" class="btn btn-outline-primary">
                        <i class="fas fa-crystal-ball"></i> Predicciones
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-clover"></i> Loto Francia
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <h3 class="text-info">{{ loto_france_count }}</h3>
                        <p class="text-muted">Sorteos registrados</p>
                    </div>
                    <div class="col-6">
                        {% if latest_loto_france %}
                            <p class="mb-1"><strong>Último sorteo:</strong></p>
                            <p class="mb-1">{{ latest_loto_france.draw_date.strftime('%d/%m/%Y') }}</p>
                            <div class="mb-2">
                                {% for number in latest_loto_france.get_main_numbers() %}
                                    <span class="number-ball">{{ number }}</span>
                                {% endfor %}
                                {% for chance in latest_loto_france.get_additional_numbers() %}
                                    <span class="number-ball chance-ball">{{ chance }}</span>
                                {% endfor %}
                            </div>
                        {% else %}
                            <p class="text-muted">No hay datos disponibles</p>
                        {% endif %}
                    </div>
                </div>
                <div class="mt-3">
                    <a href="{{ url_for('lottery_analysis', lottery_type='loto_france') }}" class="btn btn-info me-2">
                        <i class="fas fa-chart-bar"></i> Análisis
                    </a>
                    <a href="{{ url_for('predictions', lottery_type='loto_france') }}" class="btn btn-outline-info">
                        <i class="fas fa-crystal-ball"></i> Predicciones
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Features Overview -->
<div class="row mb-4">
    <div class="col-12">
        <h2 class="mb-4">Características del Sistema</h2>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-chart-line fa-3x text-primary mb-3"></i>
                <h5>Análisis Estadístico</h5>
                <p class="text-muted">
                    Frecuencias de números, patrones combinatorios, 
                    distribuciones par/impar y análisis de tendencias.
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-brain fa-3x text-success mb-3"></i>
                <h5>Modelos Avanzados</h5>
                <p class="text-muted">
                    Cadenas de Markov y redes neuronales para identificar 
                    patrones complejos en los datos históricos.
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-sync-alt fa-3x text-info mb-3"></i>
                <h5>Actualización Automática</h5>
                <p class="text-muted">
                    Conexión automática a fuentes oficiales para 
                    mantener los datos siempre actualizados.
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt"></i> Acciones Rápidas
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-primary w-100" onclick="updateData()">
                            <i class="fas fa-sync-alt"></i> Actualizar Datos
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('import_data_page') }}" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-upload"></i> Importar Datos
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-outline-success w-100" onclick="generateQuickPredictions()">
                            <i class="fas fa-magic"></i> Predicción Rápida
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('settings') }}" class="btn btn-outline-info w-100">
                            <i class="fas fa-cog"></i> Configuración
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Information -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle"></i> Información del Sistema
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success"></i> Análisis de frecuencias</li>
                    <li><i class="fas fa-check text-success"></i> Cálculos de probabilidad</li>
                    <li><i class="fas fa-check text-success"></i> Patrones combinatorios</li>
                    <li><i class="fas fa-check text-success"></i> Cadenas de Markov</li>
                    <li><i class="fas fa-check text-success"></i> Redes neuronales</li>
                    <li><i class="fas fa-check text-success"></i> Visualizaciones interactivas</li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-question-circle"></i> Cómo Usar el Sistema
                </h5>
            </div>
            <div class="card-body">
                <ol class="mb-0">
                    <li>Actualiza los datos desde fuentes oficiales</li>
                    <li>Explora el análisis estadístico de cada lotería</li>
                    <li>Revisa los patrones y frecuencias históricas</li>
                    <li>Genera predicciones usando diferentes modelos</li>
                    <li>Configura parámetros según tus preferencias</li>
                    <li>Importa datos adicionales si es necesario</li>
                </ol>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function generateQuickPredictions() {
    // Show modal to select lottery type
    const modal = `
        <div class="modal fade" id="quickPredictionModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Predicción Rápida</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p>Selecciona la lotería para generar predicciones:</p>
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" onclick="generatePrediction('euromillones')">
                                <i class="fas fa-star"></i> Euromillones
                            </button>
                            <button class="btn btn-info" onclick="generatePrediction('loto_france')">
                                <i class="fas fa-clover"></i> Loto Francia
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', modal);
    const modalElement = new bootstrap.Modal(document.getElementById('quickPredictionModal'));
    modalElement.show();
    
    // Clean up modal after hiding
    document.getElementById('quickPredictionModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function generatePrediction(lotteryType) {
    // Close modal
    bootstrap.Modal.getInstance(document.getElementById('quickPredictionModal')).hide();
    
    // Redirect to predictions page
    window.location.href = `/predictions/${lotteryType}`;
}
</script>
{% endblock %}
