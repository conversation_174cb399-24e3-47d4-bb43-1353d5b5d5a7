{% extends "base.html" %}

{% block title %}Historial - {{ lottery_type.title() }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1>
            <i class="fas fa-history"></i> 
            Historial de {{ 'Euromillones' if lottery_type == 'euromillones' else 'Loto Francia' }}
        </h1>
        <p class="text-muted">
            Consulta todos los sorteos registrados en la base de datos.
        </p>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-filter"></i> Filtros de Búsqueda
                </h5>
            </div>
            <div class="card-body">
                <form id="filterForm" method="GET">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="dateFrom" class="form-label">Desde</label>
                            <input type="date" class="form-control" id="dateFrom" name="date_from" 
                                   value="{{ request.args.get('date_from', '') }}">
                        </div>
                        <div class="col-md-3">
                            <label for="dateTo" class="form-label">Hasta</label>
                            <input type="date" class="form-control" id="dateTo" name="date_to" 
                                   value="{{ request.args.get('date_to', '') }}">
                        </div>
                        <div class="col-md-3">
                            <label for="searchNumber" class="form-label">Buscar Número</label>
                            <input type="number" class="form-control" id="searchNumber" name="search_number" 
                                   placeholder="Ej: 7" value="{{ request.args.get('search_number', '') }}">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search"></i> Filtrar
                                </button>
                                <a href="{{ url_for('history', lottery_type=lottery_type) }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Limpiar
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Summary -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-primary">{{ draws.total }}</h3>
                <p class="text-muted">Total Sorteos</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-success">{{ draws.pages }}</h3>
                <p class="text-muted">Páginas</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-info">{{ draws.page }}</h3>
                <p class="text-muted">Página Actual</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-warning">{{ draws.per_page }}</h3>
                <p class="text-muted">Por Página</p>
            </div>
        </div>
    </div>
</div>

<!-- Draws Table -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-table"></i> Sorteos Registrados
                </h5>
                <div>
                    <button class="btn btn-sm btn-outline-primary" onclick="exportData()">
                        <i class="fas fa-download"></i> Exportar
                    </button>
                </div>
            </div>
            <div class="card-body">
                {% if draws.items %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>Fecha</th>
                                    <th>Números Principales</th>
                                    <th>{{ 'Estrellas' if lottery_type == 'euromillones' else 'Chance' }}</th>
                                    <th>Bote</th>
                                    <th>Ganadores</th>
                                    <th>Acciones</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for draw in draws.items %}
                                <tr>
                                    <td>{{ draw.id }}</td>
                                    <td>{{ draw.draw_date.strftime('%d/%m/%Y') }}</td>
                                    <td>
                                        {% for number in draw.get_main_numbers() %}
                                            <span class="number-ball">{{ number }}</span>
                                        {% endfor %}
                                    </td>
                                    <td>
                                        {% for number in draw.get_additional_numbers() %}
                                            <span class="number-ball {{ 'star-ball' if lottery_type == 'euromillones' else 'chance-ball' }}">{{ number }}</span>
                                        {% endfor %}
                                    </td>
                                    <td>
                                        {% if draw.jackpot_amount %}
                                            {{ "{:,.0f}".format(draw.jackpot_amount) }} €
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if draw.winners_count %}
                                            {{ draw.winners_count }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary" 
                                                onclick="viewDrawDetails({{ draw.id }})">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-success" 
                                                onclick="copyDraw({{ draw.get_main_numbers() }}, {{ draw.get_additional_numbers() }})">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <nav aria-label="Navegación de páginas">
                        <ul class="pagination justify-content-center">
                            {% if draws.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('history', lottery_type=lottery_type, page=draws.prev_num) }}">
                                        <i class="fas fa-chevron-left"></i> Anterior
                                    </a>
                                </li>
                            {% endif %}
                            
                            {% for page_num in draws.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != draws.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('history', lottery_type=lottery_type, page=page_num) }}">
                                                {{ page_num }}
                                            </a>
                                        </li>
                                    {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                    {% endif %}
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if draws.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('history', lottery_type=lottery_type, page=draws.next_num) }}">
                                        Siguiente <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5>No se encontraron sorteos</h5>
                        <p class="text-muted">
                            {% if request.args %}
                                No hay sorteos que coincidan con los filtros aplicados.
                            {% else %}
                                No hay sorteos registrados en la base de datos.
                            {% endif %}
                        </p>
                        {% if not request.args %}
                            <a href="{{ url_for('import_data_page') }}" class="btn btn-primary">
                                <i class="fas fa-upload"></i> Importar Datos
                            </a>
                        {% endif %}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Quick Stats -->
{% if draws.items %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie"></i> Estadísticas Rápidas
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center">
                        <h4 class="text-primary" id="avgJackpot">-</h4>
                        <p class="text-muted">Bote Promedio</p>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-success" id="maxJackpot">-</h4>
                        <p class="text-muted">Bote Máximo</p>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-info" id="totalWinners">-</h4>
                        <p class="text-muted">Total Ganadores</p>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-warning" id="avgWinners">-</h4>
                        <p class="text-muted">Ganadores Promedio</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Draw Details Modal -->
<div class="modal fade" id="drawDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Detalles del Sorteo</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="drawDetailsContent">
                <!-- Draw details will be inserted here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    const lotteryType = "{{ lottery_type }}";
    
    // Calculate quick stats
    document.addEventListener('DOMContentLoaded', function() {
        calculateQuickStats();
    });

    function calculateQuickStats() {
        const rows = document.querySelectorAll('tbody tr');
        let totalJackpot = 0;
        let maxJackpot = 0;
        let totalWinners = 0;
        let jackpotCount = 0;
        let winnersCount = 0;

        rows.forEach(row => {
            const cells = row.querySelectorAll('td');
            
            // Jackpot (column 4)
            const jackpotText = cells[4].textContent.trim();
            if (jackpotText !== '-') {
                const jackpot = parseFloat(jackpotText.replace(/[€,]/g, ''));
                totalJackpot += jackpot;
                maxJackpot = Math.max(maxJackpot, jackpot);
                jackpotCount++;
            }
            
            // Winners (column 5)
            const winnersText = cells[5].textContent.trim();
            if (winnersText !== '-') {
                const winners = parseInt(winnersText);
                totalWinners += winners;
                winnersCount++;
            }
        });

        // Update display
        if (jackpotCount > 0) {
            document.getElementById('avgJackpot').textContent = 
                new Intl.NumberFormat('es-ES', { style: 'currency', currency: 'EUR', maximumFractionDigits: 0 })
                .format(totalJackpot / jackpotCount);
            document.getElementById('maxJackpot').textContent = 
                new Intl.NumberFormat('es-ES', { style: 'currency', currency: 'EUR', maximumFractionDigits: 0 })
                .format(maxJackpot);
        }
        
        document.getElementById('totalWinners').textContent = totalWinners.toLocaleString();
        
        if (winnersCount > 0) {
            document.getElementById('avgWinners').textContent = 
                (totalWinners / winnersCount).toFixed(1);
        }
    }

    function viewDrawDetails(drawId) {
        // This would fetch detailed information about the draw
        // For now, we'll show a placeholder
        document.getElementById('drawDetailsContent').innerHTML = `
            <div class="text-center">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Cargando...</span>
                </div>
                <p class="mt-2">Cargando detalles del sorteo ${drawId}...</p>
            </div>
        `;
        
        new bootstrap.Modal(document.getElementById('drawDetailsModal')).show();
        
        // Simulate API call
        setTimeout(() => {
            document.getElementById('drawDetailsContent').innerHTML = `
                <div class="alert alert-info">
                    <h6>Sorteo ID: ${drawId}</h6>
                    <p>Esta funcionalidad estará disponible en una versión futura del sistema.</p>
                    <p>Incluirá información detallada como:</p>
                    <ul>
                        <li>Análisis de frecuencias del sorteo</li>
                        <li>Comparación con predicciones anteriores</li>
                        <li>Estadísticas de números</li>
                        <li>Información de premios detallada</li>
                    </ul>
                </div>
            `;
        }, 1000);
    }

    function copyDraw(mainNumbers, additionalNumbers) {
        const text = `${mainNumbers.join(', ')} | ${additionalNumbers.join(', ')}`;
        navigator.clipboard.writeText(text).then(() => {
            showAlert('success', 'Sorteo copiado al portapapeles');
        }).catch(() => {
            showAlert('warning', 'No se pudo copiar al portapapeles');
        });
    }

    function exportData() {
        // Get current filters
        const params = new URLSearchParams(window.location.search);
        params.set('export', 'csv');
        
        // Create download link
        const url = `${window.location.pathname}?${params.toString()}`;
        window.open(url, '_blank');
        
        showAlert('info', 'Iniciando descarga de datos...');
    }
</script>
{% endblock %}
