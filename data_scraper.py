"""
Web scraping module for extracting lottery data from official sources
"""
import requests
from bs4 import BeautifulSoup
import pandas as pd
from datetime import datetime, timedelta
import re
import time
import logging
from models import db, LotteryDraw
from config import Config

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LotteryScraper:
    """Base class for lottery data scraping"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
    
    def get_page(self, url, retries=3):
        """Get webpage content with retry logic"""
        for attempt in range(retries):
            try:
                response = self.session.get(url, timeout=10)
                response.raise_for_status()
                return response
            except requests.RequestException as e:
                logger.warning(f"Attempt {attempt + 1} failed for {url}: {e}")
                if attempt < retries - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff
                else:
                    raise
    
    def parse_date(self, date_str, format_str="%d/%m/%Y"):
        """Parse date string to datetime object"""
        try:
            return datetime.strptime(date_str.strip(), format_str).date()
        except ValueError:
            logger.error(f"Could not parse date: {date_str}")
            return None

class EuromillonesScraper(LotteryScraper):
    """Scraper for Euromillones lottery data"""
    
    def __init__(self):
        super().__init__()
        self.base_url = "https://www.loteriasyapuestas.es"
        self.results_url = f"{self.base_url}/es/euromillones/resultados"
    
    def scrape_recent_results(self, num_draws=50):
        """Scrape recent Euromillones results"""
        results = []
        
        try:
            response = self.get_page(self.results_url)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find result containers (this is a simplified example - actual implementation would need to be adapted to the real website structure)
            result_containers = soup.find_all('div', class_='resultado-sorteo')
            
            for container in result_containers[:num_draws]:
                try:
                    # Extract date
                    date_element = container.find('span', class_='fecha')
                    if not date_element:
                        continue
                    
                    draw_date = self.parse_date(date_element.text)
                    if not draw_date:
                        continue
                    
                    # Extract main numbers
                    main_numbers = []
                    number_elements = container.find_all('span', class_='numero-principal')
                    for elem in number_elements:
                        try:
                            main_numbers.append(int(elem.text.strip()))
                        except ValueError:
                            continue
                    
                    # Extract stars
                    stars = []
                    star_elements = container.find_all('span', class_='estrella')
                    for elem in star_elements:
                        try:
                            stars.append(int(elem.text.strip()))
                        except ValueError:
                            continue
                    
                    if len(main_numbers) == 5 and len(stars) == 2:
                        results.append({
                            'date': draw_date,
                            'main_numbers': main_numbers,
                            'stars': stars
                        })
                
                except Exception as e:
                    logger.error(f"Error parsing Euromillones result: {e}")
                    continue
        
        except Exception as e:
            logger.error(f"Error scraping Euromillones: {e}")
        
        return results
    
    def save_results_to_db(self, results):
        """Save scraped results to database"""
        saved_count = 0
        
        for result in results:
            try:
                # Check if draw already exists
                existing = LotteryDraw.query.filter_by(
                    lottery_type='euromillones',
                    draw_date=result['date']
                ).first()
                
                if not existing:
                    draw = LotteryDraw(
                        lottery_type='euromillones',
                        draw_date=result['date'],
                        main_numbers=result['main_numbers'],
                        additional_numbers=result['stars']
                    )
                    db.session.add(draw)
                    saved_count += 1
            
            except Exception as e:
                logger.error(f"Error saving Euromillones draw: {e}")
                continue
        
        try:
            db.session.commit()
            logger.info(f"Saved {saved_count} new Euromillones draws")
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error committing Euromillones data: {e}")
        
        return saved_count

class LotoFranceScraper(LotteryScraper):
    """Scraper for French Loto data"""
    
    def __init__(self):
        super().__init__()
        self.base_url = "https://www.fdj.fr"
        self.results_url = f"{self.base_url}/jeux-de-tirage/loto/resultats"
    
    def scrape_recent_results(self, num_draws=50):
        """Scrape recent Loto France results"""
        results = []
        
        try:
            response = self.get_page(self.results_url)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find result containers (simplified example)
            result_containers = soup.find_all('div', class_='tirage-result')
            
            for container in result_containers[:num_draws]:
                try:
                    # Extract date
                    date_element = container.find('span', class_='date-tirage')
                    if not date_element:
                        continue
                    
                    draw_date = self.parse_date(date_element.text)
                    if not draw_date:
                        continue
                    
                    # Extract main numbers
                    main_numbers = []
                    number_elements = container.find_all('span', class_='numero')
                    for elem in number_elements:
                        try:
                            main_numbers.append(int(elem.text.strip()))
                        except ValueError:
                            continue
                    
                    # Extract chance number
                    chance_element = container.find('span', class_='numero-chance')
                    chance = None
                    if chance_element:
                        try:
                            chance = int(chance_element.text.strip())
                        except ValueError:
                            pass
                    
                    if len(main_numbers) == 5 and chance is not None:
                        results.append({
                            'date': draw_date,
                            'main_numbers': main_numbers,
                            'chance': [chance]
                        })
                
                except Exception as e:
                    logger.error(f"Error parsing Loto France result: {e}")
                    continue
        
        except Exception as e:
            logger.error(f"Error scraping Loto France: {e}")
        
        return results
    
    def save_results_to_db(self, results):
        """Save scraped results to database"""
        saved_count = 0
        
        for result in results:
            try:
                # Check if draw already exists
                existing = LotteryDraw.query.filter_by(
                    lottery_type='loto_france',
                    draw_date=result['date']
                ).first()
                
                if not existing:
                    draw = LotteryDraw(
                        lottery_type='loto_france',
                        draw_date=result['date'],
                        main_numbers=result['main_numbers'],
                        additional_numbers=result['chance']
                    )
                    db.session.add(draw)
                    saved_count += 1
            
            except Exception as e:
                logger.error(f"Error saving Loto France draw: {e}")
                continue
        
        try:
            db.session.commit()
            logger.info(f"Saved {saved_count} new Loto France draws")
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error committing Loto France data: {e}")
        
        return saved_count

def update_all_lottery_data():
    """Update data for all supported lotteries"""
    total_saved = 0
    
    # Update Euromillones
    euro_scraper = EuromillonesScraper()
    euro_results = euro_scraper.scrape_recent_results()
    total_saved += euro_scraper.save_results_to_db(euro_results)
    
    # Update Loto France
    loto_scraper = LotoFranceScraper()
    loto_results = loto_scraper.scrape_recent_results()
    total_saved += loto_scraper.save_results_to_db(loto_results)
    
    logger.info(f"Total new draws saved: {total_saved}")
    return total_saved
